use std::time::Duration;

use crate::errors::{WhateverError, WhateverResult};
use hass_rs::{EventData, HassClient, HassEntity, HassEvent, WSEvent};
use kameo::{
    actor::ActorRef,
    error::{ActorStopReason, BoxSendError, PanicError},
    messages,
    prelude::WeakActorRef,
    Actor,
};
use reqwest::Client;
use serde_json::json;
use snafu::ResultExt;
use tracing::{debug, error, info, warn};

use super::hass_service::HassContext;

#[derive(Debug)]
pub enum ValueType {
    State(String),     // The String represents the state name
    Attribute(String), // The String represents the attribute name
}

pub struct HassMgmt {
    ctx: HassContext,
    hass_client: Option<HassClient>,
    // TODO: Fix PubSub usage for kameo 0.16
    // state_changed_topic: ActorRef<PubSub<HassEvent>>,
    rest_client: Client,
}

impl Actor for HassMgmt {
    type Error = BoxSendError;

    async fn on_start(&mut self, actor_ref: ActorRef<Self>) -> Result<(), Self::Error> {
        debug!("Starting HASS actor");

        self.init_hass_client(actor_ref.clone())
            .await
            .map_err(|e| BoxSendError::HandlerError(Box::new(e)))?;

        Ok(())
    }

    async fn on_panic(
        &mut self,
        _actor_ref: WeakActorRef<Self>,
        error: PanicError,
    ) -> Result<std::ops::ControlFlow<ActorStopReason>, Self::Error> {
        error!("HASS actor panicked: {}", error);
        Ok(std::ops::ControlFlow::Break(ActorStopReason::Panicked(error)))
    }
}

#[messages]
impl HassMgmt {
    pub fn new(ctx: HassContext) -> Self {
        Self {
            ctx,
            hass_client: None,
            // TODO: Fix PubSub usage for kameo 0.16
            // state_changed_topic,
            rest_client: Client::new(),
        }
    }

    #[message]
    async fn handle_ws_message(&mut self, ws_event: WSEvent) -> WhateverResult<()> {
        debug!("Received WS message: {:?}", ws_event);
        self.handle_event(ws_event.event)
            .await
            .whatever_context("Failed to handle state change")?;
        // }
        Ok(())
    }

    #[message]
    pub async fn call_service(
        &mut self,
        domain: String,
        service: String,
        payload: Option<serde_json::Value>,
    ) -> WhateverResult<()> {
        debug!("Calling service: {}. {}", domain, service);
        self.hass_client
            .as_mut()
            .expect("Missing HASS client")
            .call_service(domain, service, payload)
            .await
            .map_err(|e| WhateverError::new(format!("Failed to call service: {}", e)))?;
        Ok(())
    }

    #[message]
    pub async fn publish_state(&self, entity_id: String) -> WhateverResult<()> {
        debug!("Publishing entity state.");

        let entity_state = self.get_state(entity_id.clone()).await?;

        let event = HassEvent {
            event_type: "state_changed".to_string(),
            data: EventData {
                entity_id: Some(entity_id),
                old_state: Some(entity_state.clone()),
                new_state: Some(entity_state),
            },
            time_fired: time::OffsetDateTime::now_utc()
                .format(&time::format_description::well_known::Rfc3339)
                .unwrap(),
            origin: "local".to_string(),
            context: hass_rs::Context {
                id: format!("id_{}", time::OffsetDateTime::now_utc().unix_timestamp_nanos()),
                parent_id: None,
                user_id: None,
            },
        };

        // TODO: Fix PubSub usage for kameo 0.16
        // self.state_changed_topic
        //     .ask(Publish(event))
        //     .await
        //     .whatever_context("Failed to publish state change")?;

        Ok(())
    }

    #[message]
    pub async fn get_state(&self, entity_id: String) -> WhateverResult<HassEntity> {
        let url = format!("{}/api/states/{}", self.ctx.options().rest_url, entity_id);
        debug!("Checking entity state: {}", url);

        let response = self
            .rest_client
            .get(url.clone())
            .header("Authorization", format!("Bearer {}", self.ctx.options().token))
            .timeout(std::time::Duration::from_millis(self.ctx.options().retry_delay_ms))
            .send()
            .await
            .whatever_context(format!("Failed to send request for {url}"))?;

        let state_response = response
            .json::<HassEntity>()
            .await
            .whatever_context(format!("Failed to parse response for {url}"))?;

        debug!("Entity state: {:?}", state_response);

        Ok(state_response)
    }

    #[message]
    pub async fn get_attribute(&self, entity_id: String, attribute: String) -> WhateverResult<serde_json::Value> {
        let state = self
            .get_state(entity_id.clone())
            .await
            .whatever_context("Failed to get entity state")?;

        state.attributes.get(&attribute).cloned().ok_or_else(|| {
            WhateverError::new(format!(
                "Attribute '{}' not found for entity '{}'",
                attribute, entity_id
            ))
        })
    }

    #[message]
    pub async fn set_state_or_attribute<
        T: PartialEq + std::fmt::Debug + serde::Serialize + serde::de::DeserializeOwned + Send + 'static,
    >(
        &mut self,
        entity_id: String,
        domain: String,
        service: String,
        value_type: ValueType,
        value: T,
    ) -> WhateverResult<()> {
        let mut retries = 0;

        loop {
            let state = self
                .get_state(entity_id.clone())
                .await
                .whatever_context("Failed to get state")?;

            let current_value = match &value_type {
                ValueType::State(_) => serde_json::from_value::<T>(serde_json::Value::String(state.state)).ok(),
                ValueType::Attribute(value_name) => state
                    .attributes
                    .get(value_name)
                    .and_then(|v| serde_json::from_value::<T>(v.clone()).ok()),
            };

            if let Some(current) = current_value {
                if current == value {
                    debug!("'{:?}' for '{:?}' is set to {:?}", value_type, entity_id, value);
                    break;
                }
                info!(
                    "Setting '{:?}' for '{:?}' from {:?} to {:?}",
                    value_type, entity_id, current, value
                );

                let payload = match &value_type {
                    ValueType::State(state_name) => json!({
                        "entity_id": entity_id,
                        state_name: value
                    }),
                    ValueType::Attribute(value_name) => json!({
                        "entity_id": entity_id,
                        value_name: value
                    }),
                };

                self.call_service(domain.clone(), service.clone(), Some(payload))
                    .await
                    .whatever_context("Failed to call service")?;
            } else {
                warn!(
                    "Could not get current '{:?}' for '{:?}'. Failing...",
                    value_type, entity_id
                );
            }

            retries += 1;
            if retries >= self.ctx.options().max_retries {
                warn!("Failed to set '{:?}' to {:?} for '{:?}'", value_type, value, entity_id);
                break;
            }
            tokio::time::sleep(Duration::from_millis(self.ctx.options().retry_delay_ms)).await;
        }
        Ok(())
    }

    async fn handle_event(&mut self, event: HassEvent) -> WhateverResult<()> {
        debug!("Sending state change to subscribed handlers: {:?}", event);
        // TODO: Fix PubSub usage for kameo 0.16
        // self.state_changed_topic
        //     .ask(Publish(event))
        //     .await
        //     .whatever_context("Failed to publish state change")?;
        Ok(())
    }

    async fn init_hass_client(&mut self, hass_actor: ActorRef<HassMgmt>) -> WhateverResult<()> {
        self.hass_client = Some(
            HassClient::new(&self.ctx.options().ws_url)
                .await
                .map_err(|e| WhateverError::new(format!("Failed to create Home Assistant client: {}", e)))?,
        );

        debug!(
            "Authenticating with Home Assistant using token: {}",
            self.ctx.options().token
        );
        self.hass_client
            .as_mut()
            .unwrap()
            .auth_with_longlivedtoken(&self.ctx.options().token)
            .await
            .map_err(|e| WhateverError::new(format!("Failed to authenticate with Home Assistant: {}", e)))?;
        let mut event_receiver = self
            .hass_client
            .as_mut()
            .unwrap()
            .subscribe_event("state_changed")
            .await
            .map_err(|e| WhateverError::new(format!("Failed to subscribe to Home Assistant events: {}", e)))?;

        tokio::spawn(async move {
            while let Some(ws_event) = event_receiver.recv().await {
                if hass_actor.is_alive() {
                    if let Err(e) = hass_actor.tell(HandleWsMessage { ws_event }).await {
                        error!("Failed to send message to actor: {:?}", e);
                        break;
                    }
                }
            }
        });

        Ok(())
    }
}
