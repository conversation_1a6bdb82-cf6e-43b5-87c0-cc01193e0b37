use crate::{errors::WhateverResult, hass::<PERSON><PERSON><PERSON><PERSON><PERSON>};
use hass_rs::HassEvent;
use kameo::{
    actor::{ActorID, ActorRef, WeakActorRef},
    error::{ActorStopReason, BoxSendError},
    messages, Actor,
};
use kameo_actors::pubsub::PubSub;
use std::time::Duration;
use tracing::{debug, info, warn};

use super::{hass_mgmt::HassMgmt, hass_service::HassContext};

pub struct HassServiceSupervisor {
    ctx: HassContext,
    hass_mgmt: Option<ActorRef<HassMgmt>>,
    pub(crate) state_changed_topic: Option<ActorRef<PubSub<HassEvent>>>,
}

#[messages]
impl HassServiceSupervisor {
    #[message]
    pub async fn get_hass_mgmt(&self) -> WhateverResult<ActorRef<HassMgmt>> {
        debug!(
            "[Supervisor] Providing hass mgmt: {:?}",
            self.hass_mgmt.as_ref().unwrap()
        );

        Ok(self.hass_mgmt.as_ref().unwrap().clone())
    }

    pub fn new(ctx: HassContext) -> Self {
        HassServiceSupervisor {
            ctx,
            hass_mgmt: None,
            state_changed_topic: None,
        }
    }

    async fn start_hass_mgmt(
        &self,
        supervisor: &ActorRef<HassServiceSupervisor>,
    ) -> WhateverResult<ActorRef<HassMgmt>> {
        let hass_mgmt = kameo::spawn(HassMgmt::new(
            self.ctx.clone(),
            self.state_changed_topic.as_ref().unwrap().clone(),
        ));
        supervisor.link(&hass_mgmt).await;

        Ok(hass_mgmt)
    }
}

impl Actor for HassServiceSupervisor {
    type Error = BoxSendError;

    async fn on_start(&mut self, actor_ref: ActorRef<Self>) -> Result<(), Self::Error> {
        debug!("Starting supervisor");

        let actor_ref = actor_ref.clone();

        self.state_changed_topic = Some(kameo::spawn(PubSub::new()));
        self.hass_mgmt = Some(
            self.start_hass_mgmt(&actor_ref)
                .await
                .map_err(|e| BoxSendError::HandlerError(Box::new(e)))?,
        );

        let agents = self.ctx.agents().get_all::<dyn HassHandler>().collect::<Vec<_>>();
        for agent in agents {
            agent
                .start(
                    actor_ref.clone(),
                    self.state_changed_topic.as_ref().unwrap().clone(),
                    self.ctx.clone(),
                )
                .await
                .map_err(|e| BoxSendError::HandlerError(Box::new(e)))?;
        }

        Ok(())
    }

    async fn on_stop(&mut self, _: WeakActorRef<Self>, reason: ActorStopReason) -> Result<(), Self::Error> {
        info!("Stopping or restarting supervisor: {:?}", reason);

        // Stop HASS actor
        if let Some(hass_mgmt) = &self.hass_mgmt {
            if hass_mgmt.is_alive() {
                hass_mgmt.kill();
            }
        }

        // Stop state check topic
        if let Some(topic) = &self.state_changed_topic {
            if topic.is_alive() {
                topic.kill();
            }
        }

        // Stop handlers registry
        let agents = self.ctx.agents().get_all::<dyn HassHandler>().collect::<Vec<_>>();
        for agent in agents {
            agent
                .stop(Some("Supervisor stopped".to_string()))
                .await
                .map_err(|e| BoxSendError::HandlerError(Box::new(e)))?;
        }

        Ok(())
    }

    async fn on_link_died(
        &mut self,
        actor_ref: WeakActorRef<Self>,
        id: ActorID,
        reason: ActorStopReason,
    ) -> Result<std::ops::ControlFlow<ActorStopReason>, Self::Error> {
        // Return Continue to continue running, or Break(reason) to stop the supervisor
        match reason {
            ActorStopReason::Panicked(_) => {
                warn!(
                    "Restarting all actors due to link died with panic: actorId: {:?}, reason: {:?}",
                    id, reason
                );
                self.on_stop(actor_ref.clone(), reason).await?;

                tokio::time::sleep(Duration::from_millis(self.ctx.options().retry_delay_ms)).await;

                // upgrade the weak reference to a strong reference
                let actor_ref = actor_ref.upgrade().unwrap();
                self.on_start(actor_ref).await?;

                Ok(std::ops::ControlFlow::Continue(()))
            }
            ActorStopReason::Normal | ActorStopReason::Killed => Ok(std::ops::ControlFlow::Continue(())),
            _ => Ok(std::ops::ControlFlow::Break(reason)),
        }
    }
}
