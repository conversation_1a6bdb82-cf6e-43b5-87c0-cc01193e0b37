use async_trait::async_trait;
use hass_rs::HassEvent;
use hass_service::HassContext;
use hass_supervisor::HassServiceSupervisor;
use kameo::actor::ActorRef;
use kameo_actors::pubsub::PubSub;

use crate::errors::WhateverResult;

pub mod hass_jobs;
pub mod hass_mgmt;
pub mod hass_options;
pub mod hass_service;
pub mod hass_supervisor;

#[async_trait]
pub trait HassHandler: Send + Sync + 'static {
    async fn start(
        &self,
        supervisor: ActorRef<HassServiceSupervisor>,
        topic: ActorRef<PubSub<HassEvent>>,
        ctx: HassContext,
    ) -> WhateverResult<()>;

    async fn stop(&self, reason: Option<String>) -> WhateverResult<()>;
}
