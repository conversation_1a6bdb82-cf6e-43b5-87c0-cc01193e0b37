use async_trait::async_trait;
use config::ext::{EnvironmentVariablesExtensions, YamlConfigurationExtensions};
use config::{Configuration, ConfigurationBuilder, DefaultConfigurationBuilder};
use di::{ServiceCollection, ServiceDescriptor, ServiceProvider, Type};
use options::ext::OptionsConfigurationServiceExtensions;
use options::Ref;
use snafu::ResultExt;
use std::any::Any;
use std::path::PathBuf;
use std::sync::Arc;
use tracing::{debug, trace};

use crate::hass::hass_options::HassOptions;
use crate::{errors::WhateverResult, whatever_trace};

pub type BuilderResult<T> = WhateverResult<T>;

// -- Service prt ----
#[async_trait]
pub trait Service: Send + Sync + 'static {
    async fn start(&self, ctx: AppContext) -> WhateverResult<()>;
    async fn stop(&self) -> WhateverResult<()>;
}

// -- App part ----
// pub trait AppOptions: Send + Sync + 'static {}

#[derive(<PERSON><PERSON>, Default)]
pub struct AppContext {
    // config: Arc<dyn AppConfig>,
    pub services: Arc<ServiceProvider>,
}

impl AppContext {
    // pub fn config<T: AppConfig + Clone>(&self) -> Box<dyn AppConfig> {
    //     Box::new(self.service::<T>().expect("Failed to get config").as_ref().clone())
    // }

    pub fn services(&self) -> ServiceProvider {
        (*self.services).clone()
    }

    pub fn service<T: Any + ?Sized>(&self) -> Option<Ref<T>> {
        Some((*self.services).get::<T>().unwrap())
    }
}

#[derive(Default)]
pub struct AppBuilder {
    config_builder: DefaultConfigurationBuilder,
    config: Option<Ref<dyn Configuration>>,
    service_coll: ServiceCollection,
}

impl AppBuilder {
    pub fn new() -> Self {
        Self {
            config_builder: DefaultConfigurationBuilder::new(),
            config: None,
            service_coll: ServiceCollection::new(),
        }
    }

    pub fn add_config_yaml(&mut self, path_to_config: PathBuf) -> BuilderResult<&mut Self> {
        self.config_builder.add_yaml_file(path_to_config);

        Ok(self)
    }

    pub fn add_config_env(&mut self, prefix: &str) -> BuilderResult<&mut Self> {
        self.config_builder.add_env_vars_with_prefix(prefix);

        Ok(self)
    }

    pub fn add_services<T: Into<ServiceDescriptor>>(&mut self, services: Vec<T>) -> BuilderResult<&mut Self> {
        for service in services {
            self.service_coll.add(service);
        }
        Ok(self)
    }

    // Add proper build() method that consumes self
    pub fn build(&mut self) -> BuilderResult<App> {
        let config_result = self
            .config_builder
            .build()
            .or_else(|e| whatever_trace!("Failed to build config: {:?}", e))?;

        if tracing::level_enabled!(tracing::Level::TRACE) {
            for (key, value) in config_result.iter(None) {
                trace!("Config item: {} = {}", key, value.as_str());
            }
        }

        self.config = Some(Ref::from(config_result.as_config()));

        self.service_coll.apply_config::<HassOptions>(
            self.config
                .as_ref()
                .expect("Config is not built")
                .section("HassOptions")
                .as_config()
                .into(),
        );

        let provider = self
            .service_coll
            .build_provider()
            .whatever_context("Failed to build services")?;

        Ok(App {
            ctx: AppContext {
                // config: Arc::new(provider.get_required::<HassConfigService>().values()),
                services: Arc::new(provider),
            },
        })
    }
}

#[derive(Default)]
pub struct App {
    pub ctx: AppContext,
}

impl App {
    pub fn get_context(&self) -> AppContext {
        self.ctx.clone()
    }

    pub async fn start_service<T: Service + ?Sized>(&self) -> BuilderResult<()> {
        debug!(
            "Starting services type: {}, count: {}",
            Type::of::<T>(),
            self.ctx.services.get_all::<T>().count()
        );
        // Iterate through all descriptors and start each service
        for service in self.ctx.services.get_all::<T>() {
            debug!("Starting service");
            service
                .as_ref()
                .start(self.get_context())
                .await
                .whatever_context("Failed to start service".to_string())?;
        }

        Ok(())
    }

    pub async fn stop_service<T: Service + ?Sized>(&self) -> BuilderResult<()> {
        debug!(
            "Stoping services type: {}, count: {}",
            Type::of::<T>(),
            self.ctx.services.get_all::<T>().count()
        );
        // Iterate through all descriptors and start each service
        for service in self.ctx.services.get_all::<T>() {
            service
                .as_ref()
                .stop()
                .await
                .whatever_context("Failed to start service".to_string())?;
        }

        Ok(())
    }
}
