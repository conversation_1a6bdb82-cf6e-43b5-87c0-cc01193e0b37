[package]
name = "hag-core"
version = "0.1.0"
edition = "2021"
description = "Home Assistant Agentic Platform"
license = "MIT"
readme = "README.md"
include = ["README.md", "LICENSE*"]

[dependencies]
async-trait = "0.1"
parking_lot = "0.12"
time = { version = "0.3", features = ["local-offset", "macros"] }
snafu = { version = "0.8" }
tokio = { version = "1.4", default-features = false, features = [
  "rt-multi-thread",
  "signal",
  "tracing",
] }
tracing = { version = "0.1", features = ["attributes"] }
tracing-subscriber = { version = "0.3", features = ["local-time"] }
kameo = { version = "0.16" }
kameo_actors = { version = "0.1" }
tokio-cron-scheduler = { version = "0.14", features = ["english"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
more-di = { version = "3.1", features = ["fmt", "async"] }
more-config = { git = "https://github.com/jluzny/more-rs-config.git", branch = "feature/yaml-config-source", features = [
  "async",
  "env",
  "yaml",
  "chained",
] }
more-options = { version = "3.3", features = ["di", "cfg", "async"] }
reqwest = { version = "0.12", default-features = false, features = [
  "json",
  "rustls-tls",
] }
hass-rs = "0.4"


[dev-dependencies]
# tokio = { version = "1", features = ["rt", "test-util"] }
# tracing-test = "0.2"


# [[test]]
# name = "errors"
# path = "src/errors.rs"
