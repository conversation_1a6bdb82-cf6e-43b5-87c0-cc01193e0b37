[package]
name = "hag-hvac"
version = "0.1.0"
edition = "2021"
description = "Home Assistant HVAC Controlapplication"
license = "MIT"
readme = "README.md"
include = ["README.md", "LICENSE*"]

[dependencies]
hag-core = { path = "../hag-core" }
async-trait = "0.1"
parking_lot = "0.12"
snafu = { version = "0.8" }
time = { version = "0.3", features = ["local-offset", "macros"] }
tokio = { version = "1.4", default-features = false, features = [
  "rt-multi-thread",
  "signal",
  "tracing",
] }
serde = { version = "1", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
tracing = { version = "0.1", features = ["attributes"] }
tracing-subscriber = { version = "0.3", features = ["local-time"] }
more-di = { version = "3.1", features = ["fmt", "async"] }
more-config = { git = "https://github.com/jluzny/more-rs-config.git", branch = "feature/yaml-config-source", features = [
  "async",
  "env",
  "yaml",
  "chained",
] }
more-options = { version = "3.3", features = ["di", "cfg", "async"] }
kameo = "0.16"
kameo_actors = "0.1"
smlang = "0.8"
hass-rs = "0.4"


[[bin]]
name = "hag-hvac"
path = "src/main.rs"

# [[test]]
# name = "config_test"
# path = "src/tests/config_test.rs"

# [[test]]
# name = "main_test"
# path = "src/tests/main_test.rs"
