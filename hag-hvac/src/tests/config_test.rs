#[cfg(test)]
mod tests {
    use config::{
        ext::{EnvironmentVariablesExtensions, YamlConfigurationExtensions},
        Configuration, ConfigurationBuilder, DefaultConfigurationBuilder,
    };
    use di::{Injectable, ServiceCollection};
    use hag_core::{
        core::AppBuilder,
        errors::WhateverResult,
        hass::hass_options::{HassOptions, HassOptionsService},
        whatever_trace,
    };
    use options::{ext::OptionsConfigurationServiceExtensions, Ref};
    use snafu::ResultExt;
    use std::{env, path::PathBuf};
    use tracing::trace;

    use crate::hvac_options::{HvacOptions, HvacOptionsService};

    #[test]
    fn test_manual_bootstrap() -> WhateverResult<()> {
        let config_dir = PathBuf::from("config");
        let test_config_path = config_dir.join("config_test.yaml");

        // Set test environment variables
        let token_env = env::var("HASS_HassOptions__Token").expect("HASS_HassOptions__Token not set");

        let config_result = DefaultConfigurationBuilder::new()
            .add_yaml_file(test_config_path.clone())
            .add_env_vars_with_prefix("HASS_")
            .build()
            .or_else(|e| whatever_trace!("Failed to build config: {:?}", e))?;

        if tracing::level_enabled!(tracing::Level::TRACE) {
            for (key, value) in config_result.iter(None) {
                trace!("Config item: {} = {}", key, value.as_str());
            }
        }

        let mut service_coll = ServiceCollection::new();
        service_coll
            .add(HassOptionsService::singleton())
            .add(HvacOptionsService::singleton());

        let config: Ref<dyn Configuration> = Ref::from(config_result.as_config());

        service_coll.apply_config::<HassOptions>(config.section("HassOptions").as_config().into());
        service_coll.apply_config::<HvacOptions>(config.section("HvacOptions").as_config().into());

        let provider = service_coll
            .build_provider()
            .whatever_context("Failed to build services")?;

        let hass_options = provider
            .get::<HassOptionsService>()
            .expect("HassOptionsService not found");
        let hvac_options = provider
            .get::<HvacOptionsService>()
            .expect("HvacOptionsService not found");

        // Test JSON config values
        assert_eq!(hass_options.options().rest_url, "http://192.168.0.204:8123");

        // Test first HVAC entity
        let hvac1 = &hvac_options.options().hvac_entities[0];
        assert_eq!(hvac1.preset_mode, "quiet");

        // Test environment variable override
        assert_eq!(hass_options.options().token, token_env);

        Ok(())
    }

    #[test]
    fn test_app_builder() -> WhateverResult<()> {
        // This test demonstrates that AppBuilder only supports HassOptions out of the box
        // For application-specific options like HvacOptions, use manual configuration
        // as shown in test_manual_bootstrap

        let config_dir = PathBuf::from("config");
        let test_config_path = config_dir.join("config_test.yaml");

        // Set test environment variables
        let token_env = env::var("HASS_HassOptions__Token").expect("HASS_HassOptions__Token not set");

        // AppBuilder only configures HassOptions automatically
        let app = AppBuilder::new()
            .add_config_yaml(test_config_path.clone())?
            .add_config_env("HASS_")?
            .add_services(vec![HassOptionsService::singleton()])?
            .build()?;

        let hass_options = app.ctx.services.get::<HassOptionsService>().unwrap();

        // Test JSON config values
        assert_eq!(hass_options.options().rest_url, "http://192.168.0.204:8123");

        // Test environment variable override
        assert_eq!(hass_options.options().token, token_env);

        Ok(())
    }
}
