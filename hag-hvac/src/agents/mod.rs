use async_trait::async_trait;
use hag_core::hass::{hass_service::Hass<PERSON><PERSON>x<PERSON>, hass_supervisor::HassServiceSupervisor};
use hass_rs::HassEvent;
use kameo::actor::ActorRef;
use kameo_actors::pubsub::PubSub;

use hag_core::errors::WhateverResult;

pub mod hvac_agent;

#[async_trait]
pub trait HassHandler: Send + Sync + 'static {
    async fn start(
        &self,
        supervisor: ActorRef<HassServiceSupervisor>,
        topic: ActorRef<PubSub<HassEvent>>,
        ctx: HassContext,
    ) -> WhateverResult<()>;

    async fn stop(&self, reason: Option<String>) -> WhateverResult<()>;
}
