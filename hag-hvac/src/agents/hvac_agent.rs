use async_trait::async_trait;
use di::{injectable, ServiceProvider};
use hag_core::{
    errors::*,
    hass::{hass_jobs::HassJob<PERSON>, hass_mgmt::GetState, hass_service::HassContext, hass_supervisor::GetHassMgmt},
    utils::scheduler_service::SchedulerService,
    whatever_trace,
};
use hass_rs::HassEvent;
use kameo::{
    actor::ActorRef,
    message::{Context, Message},
    Actor,
};
use kameo_actors::pubsub::{PubSub, Subscribe};
use parking_lot::RwLock;
use smlang::statemachine;
use snafu::ResultExt;
use std::{ops::ControlFlow, time::Duration};
use time::OffsetDateTime as DateTime;
use tracing::{debug, info};

use hag_core::{
    hass::hass_mgmt::{GetAttribute, HassMgmt, SetStateOrAttribute, ValueType},
    hass::hass_supervisor::HassServiceSupervisor,
    hass::Has<PERSON><PERSON><PERSON><PERSON>,
};

use crate::hvac_options::{HvacOptions, HvacOptionsService};

#[derive(Clone)]
pub struct HvacContext {
    pub options: HvacOptions,
    services: ServiceProvider,
    supervisor: ActorRef<HassServiceSupervisor>,
}

// -- Handler implementation --
#[injectable(HassHandler)]
pub struct HvacHandler {
    options: HvacOptions,
    actor_ref: RwLock<Option<ActorRef<HvacActor>>>,
}

#[async_trait]
impl HassHandler for HvacHandler {
    async fn start(
        &self,
        supervisor: ActorRef<HassServiceSupervisor>,
        topic: ActorRef<PubSub<HassEvent>>,
        ctx: HassContext,
    ) -> WhateverResult<()> {
        let hvac_ctx = HvacContext {
            options: ctx.services().get_required::<HvacOptionsService>().options(),
            services: ctx.services(),
            supervisor: supervisor.clone(),
        };

        let actor_ref = kameo::spawn(HvacActor::new(hvac_ctx).await?);

        supervisor.link(&actor_ref).await;

        topic
            .ask(Subscribe(actor_ref.clone()))
            .await
            .whatever_context("Failed to subscribe to state changes")?;

        *self.actor_ref.write() = Some(actor_ref);

        // trigger initial state check
        ctx.services()
            .get_required::<SchedulerService>()
            .add_job(
                ctx.services()
                    .get_required::<HassJobs>()
                    .publish_state_oneshot(
                        Duration::from_millis(ctx.options().retry_delay_ms),
                        // ctx.options().temp_sensor,
                        self.options.temp_sensor.clone(),
                    )
                    .await?,
            )
            .await?;

        Ok(())
    }

    async fn stop(&self, _reason: Option<String>) -> WhateverResult<()> {
        info!("Immidiately Stopping HVAC handler");

        let actor_ref = self.actor_ref.read().clone();
        if let Some(actor_ref) = actor_ref {
            if actor_ref.is_alive() {
                actor_ref
                    .stop_gracefully()
                    .await
                    .whatever_context("Failed to stop HVAC actor")?;
            }
        }

        Ok(())
    }
}

// -- State Machine implementation --
statemachine! {
    derive_states: [Debug, Clone],
    custom_error: true,
    transitions: {
        // *Init + StateChanged(StateChangeData) / async switch_off = Off,
        *Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low && need_defrost_cycle] / async start_defrost = Defrosting,
        Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low] / async start_or_stay_heating = Heating,
        Off + StateChanged(StateChangeData) [!can_operate || is_temp_too_high || (!is_temp_too_low && !is_temp_too_high)] / async switch_or_stay_off = Off,

        Heating + StateChanged(StateChangeData) [can_operate && need_defrost_cycle] / async start_defrost = Defrosting,
        Heating + StateChanged(StateChangeData) [!can_operate || is_temp_too_high] / async switch_or_stay_off = Off,
        Heating + StateChanged(StateChangeData) [can_operate && (is_temp_too_low || !is_temp_too_high)] / async start_or_stay_heating = Heating,

        Defrosting + StateChanged(StateChangeData) [is_defrost_cycle_completed] / async stop_defrost = Off,
        Defrosting + StateChanged(StateChangeData) [!is_defrost_cycle_completed] / continue_defrost = Defrosting,
        Defrosting + StateChanged(StateChangeData) [!can_operate]  / async switch_or_stay_off = Off,
    }
}

pub struct SMContext {
    ctx: HvacContext,
    defrost_last: RwLock<Option<DateTime>>,
    defrost_current: RwLock<Option<DateTime>>,
}

impl SMContext {
    fn new(ctx: HvacContext) -> Self {
        Self {
            ctx,
            defrost_last: RwLock::new(None),
            defrost_current: RwLock::new(None),
        }
    }

    async fn set_units_mode_and_preset<F>(
        &self,
        hass_actor: ActorRef<HassMgmt>,
        eval_attr_name: &str,
        eval_rule: F,
    ) -> WhateverResult<()>
    where
        F: Fn(&serde_json::Value, usize) -> ControlFlow<(), Option<String>>, // Add entity_index to closure
    {
        for (entity_index, entity) in self
            .ctx
            .options
            .hvac_entities
            .iter()
            .enumerate()
            .filter(|(_, e)| e.enabled)
        {
            let eval_attr_value = hass_actor
                .ask(GetAttribute {
                    entity_id: entity.entity_id.clone(),
                    attribute: eval_attr_name.to_string(),
                })
                .await
                .whatever_context(format!("Failed to get attribute {}", eval_attr_name))?;

            let control_flow = eval_rule(&eval_attr_value, entity_index); // Pass entity_index to closure
            debug!(
                "Control flow for: {:?},  {:?}={:?} -> {:?}",
                entity.entity_id, eval_attr_name, eval_attr_value, control_flow
            );

            match control_flow {
                ControlFlow::Break(_) => break,
                ControlFlow::Continue(Some(mode)) => {
                    // Set HVAC mode
                    hass_actor
                        .ask(SetStateOrAttribute {
                            entity_id: entity.entity_id.clone(),
                            domain: "climate".to_string(),
                            service: "set_hvac_mode".to_string(),
                            value_type: ValueType::State("hvac_mode".to_string()),
                            value: mode.clone(),
                        })
                        .await
                        .whatever_context(format!("Failed to set HVAC mode to {}", mode))?;

                    // Apply preset mode
                    hass_actor
                        .ask(SetStateOrAttribute {
                            entity_id: entity.entity_id.clone(),
                            domain: "climate".to_string(),
                            service: "set_preset_mode".to_string(),
                            value_type: ValueType::Attribute("preset_mode".to_owned()),
                            value: entity.preset_mode.clone(),
                        })
                        .await
                        .whatever_context("Failed to set preset mode")?;

                    // Set temperature
                    hass_actor
                        .ask(SetStateOrAttribute {
                            entity_id: entity.entity_id.clone(),
                            domain: "climate".to_string(),
                            service: "set_temperature".to_string(),
                            value_type: ValueType::Attribute("temperature".to_string()),
                            value: entity.temperature,
                        })
                        .await
                        .whatever_context("Failed to set temperature")?;
                }
                ControlFlow::Continue(None) => continue,
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct StateChangeData {
    state_mgmt: ActorRef<HassMgmt>,
    pub weather_temp: f64,
    pub current_temp: f64,
    pub hour: u8,
    pub is_weekday: bool,
}

impl StateMachineContext for SMContext {
    type Error = WhateverError;

    fn can_operate(&self, data: &StateChangeData) -> WhateverResult<bool> {
        // Check both weather compatibility and active hours
        let weather_ok = data.weather_temp > self.ctx.options.temperature_thresholds.outdoor_min;

        let start_hour = if data.is_weekday {
            self.ctx.options.active_hours.start_weekday
        } else {
            self.ctx.options.active_hours.start
        };
        let hours_ok = (start_hour..=self.ctx.options.active_hours.end).contains(&data.hour);

        debug!("Can operate: weather_ok: {weather_ok}, hours_ok: {hours_ok}");

        Ok(weather_ok && hours_ok)
    }

    fn is_temp_too_low(&self, data: &StateChangeData) -> WhateverResult<bool> {
        Ok(data.current_temp < self.ctx.options.temperature_thresholds.min)
    }

    fn is_temp_too_high(&self, data: &StateChangeData) -> WhateverResult<bool, WhateverError> {
        Ok(data.current_temp > self.ctx.options.temperature_thresholds.max)
    }

    async fn start_or_stay_heating(&mut self, data: StateChangeData) -> WhateverResult<()> {
        info!(
            "Starting or staying HEATING: temp:{:.1}°C, weather:{:.1}°C at hour:{}",
            data.current_temp, data.weather_temp, data.hour
        );

        self.set_units_mode_and_preset(data.state_mgmt.clone(), "current_temperature", |current_temp, idx| {
            let threshold = self.ctx.options.hvac_entities[idx].temperature;

            ControlFlow::Continue(Some(
                if current_temp.as_f64().unwrap() <= threshold {
                    "heat"
                } else {
                    "off"
                }
                .into(),
            ))
        })
        .await
    }

    async fn switch_or_stay_off(&mut self, data: StateChangeData) -> WhateverResult<()> {
        info!(
            "Switching or staying OFF: temp:{:.1}°C, weather:{:.1}°C at hour:{}",
            data.current_temp, data.weather_temp, data.hour
        );

        self.set_units_mode_and_preset(data.state_mgmt.clone(), "current_temperature", |_, _idx| {
            ControlFlow::Continue(Some("off".into()))
        })
        .await
    }

    fn need_defrost_cycle(&self, data: &StateChangeData) -> WhateverResult<bool> {
        let now = DateTime::now_local().expect("Failed to get local time");
        let period = Duration::from_secs(self.ctx.options.defrost.period_seconds);
        let temperature_threshold = self.ctx.options.defrost.temperature_threshold;

        let result = match (data.weather_temp, *self.defrost_last.read()) {
            (temp, _) if temp > temperature_threshold => false,
            (_, Some(last)) if now - last < period => false,
            _ => true,
        };

        info!(
            "DEFROST cycle needed: {result}, weather_temp:{:?}°C, defrost_last:{:?}",
            data.weather_temp, self.defrost_last,
        );
        Ok(result)
    }

    async fn start_defrost(&mut self, data: StateChangeData) -> WhateverResult<()> {
        let now = DateTime::now_local().expect("Failed to get local time");

        info!(
            "Starting DEFROST cycle: temp:{:.1}°C, weather:{:.1}°C at hour:{}",
            data.current_temp, data.weather_temp, data.hour
        );

        *self.defrost_current.write() = Some(now);

        // Only set defrost mode for units that have defrost=true in config
        for entity in self.ctx.options.hvac_entities.iter().filter(|e| e.enabled) {
            let mode = if entity.defrost { "cool" } else { "off" };

            info!("Setting '{}' to '{}' mode during defrost cycle", entity.entity_id, mode);
            data.state_mgmt
                .ask(SetStateOrAttribute {
                    entity_id: entity.entity_id.clone(),
                    domain: "climate".to_string(),
                    service: "set_hvac_mode".to_string(),
                    value_type: ValueType::State("hvac_mode".to_string()), // (true, "hvac_mode".to_string()),
                    value: mode.to_string(),
                })
                .await
                .whatever_context("Failed to set HVAC mode")?;
        }

        // schedule event to stop defrost cycle
        self.ctx
            .services
            .get_required::<SchedulerService>()
            .add_job(
                self.ctx
                    .services
                    .get_required::<HassJobs>()
                    .publish_state_oneshot(
                        Duration::from_secs(self.ctx.options.defrost.duration_seconds),
                        self.ctx.options.temp_sensor.clone(),
                    )
                    .await?,
            )
            .await?;

        Ok(())
    }

    fn is_defrost_cycle_completed(&self, _data: &StateChangeData) -> WhateverResult<bool> {
        let now = DateTime::now_local().expect("Failed to get local time");
        let duration = Duration::from_secs(self.ctx.options.defrost.duration_seconds);

        let defrost_completed = match &*self.defrost_current.read() {
            Some(current) => {
                let elapsed = now - *current;
                elapsed >= duration
            }
            None => {
                info!("Defrost check: No current defrost cycle in progress");
                false
            }
        };

        Ok(defrost_completed)
    }

    fn continue_defrost(&mut self, _data: StateChangeData) -> WhateverResult<()> {
        let now = DateTime::now_local().expect("Failed to get local time");

        let elapsed = match *self.defrost_current.read() {
            Some(start) => now - start,
            None => time::Duration::ZERO,
        };

        let total_duration = time::Duration::seconds(self.ctx.options.defrost.duration_seconds as i64);
        let remaining = total_duration - elapsed;

        info!(
            "DEFROST in progress: {} total_duration, {} minutes elapsed, {} remaining",
            total_duration.whole_minutes(),
            elapsed.whole_minutes(),
            remaining.whole_minutes()
        );
        Ok(())
    }

    async fn stop_defrost(&mut self, data: StateChangeData) -> WhateverResult<()> {
        info!(
            "Stoping DEFROST: temp:{}°C, weather:{}°C, duration:{} minutes",
            data.current_temp,
            data.weather_temp,
            (DateTime::now_local().expect("Failed to get local time")
                - (*self.defrost_current.read()).expect("Defrost cycle not started"))
            .whole_minutes()
        );

        *self.defrost_last.write() = Some(DateTime::now_local().expect("Failed to get local time"));

        self.switch_or_stay_off(data).await?;

        // schedule event to ensure next defrost cycle is triggered
        self.ctx
            .services
            .get_required::<SchedulerService>()
            .add_job(
                self.ctx
                    .services
                    .get_required::<HassJobs>()
                    .publish_state_oneshot(
                        std::time::Duration::from_secs(self.ctx.options.defrost.period_seconds),
                        self.ctx.options.temp_sensor.clone(),
                    )
                    .await?,
            )
            .await?;

        Ok(())
    }
}

// -- Actor implementation --
#[derive(Actor)]
pub struct HvacActor {
    pub(crate) ctx: HvacContext,
    state_machine: StateMachine<SMContext>,
}

impl Message<HassEvent> for HvacActor {
    type Reply = WhateverResult<()>;

    async fn handle(&mut self, event: HassEvent, _ctx: &mut Context<Self, Self::Reply>) -> Self::Reply {
        if event.clone().data.entity_id.expect("Missing entity id") != self.ctx.options.temp_sensor {
            return Ok(());
        } else {
            debug!(
                "Handling state change for {:?}, with states {:?} -> {:?}",
                event.data.entity_id, event.data.old_state, event.data.new_state
            );
        }

        let hass_mgmt = self
            .ctx
            .supervisor
            .ask(GetHassMgmt)
            .await
            .whatever_context("Failed to get state management actor")?;

        let weather_temp = hass_mgmt
            .ask(GetState {
                entity_id: "sensor.openweathermap_temperature".into(),
            })
            .await
            .whatever_context("Failed to get weather temperature")?
            .state
            .parse::<f64>()
            .whatever_context("Failed to parse weather temperature")?;

        let current_temp = event
            .data
            .new_state
            .expect("Failed to get new state")
            .state
            .parse::<f64>()
            .whatever_context("Failed to parse temperature")?;

        let now = DateTime::now_local().expect("Failed to get local time");

        let state_data = StateChangeData {
            state_mgmt: hass_mgmt,
            weather_temp,
            current_temp,
            hour: now.hour(),
            is_weekday: now.weekday().number_from_monday() <= 5,
        };

        let before_state = self.state_machine.state().clone();

        self.state_machine
            .process_event(Events::StateChanged(state_data))
            .await
            .or_else(|e| whatever_trace!("Failed to process event: {:?}", e))?;

        let after_state = self.state_machine.state().clone();

        if before_state != after_state {
            info!("State has changed from {:?} to {:?}", before_state, after_state);
        }

        Ok(())
    }
}

impl HvacActor {
    async fn new(ctx: HvacContext) -> WhateverResult<Self> {
        Ok(Self {
            ctx: ctx.clone(),
            state_machine: StateMachine::new(SMContext::new(ctx)),
        })
    }
}
