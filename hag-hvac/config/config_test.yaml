HassOptions:
  WsUrl: ws://192.168.0.204:8123/api/websocket
  # WsUrlProxy: https://94mzt2ecyq0sud43fdto7supfzisp1y8.ui.nabu.casa
  RestUrl: http://192.168.0.204:8123
  # RestUrlProxy: https://94mzt2ecyq0sud43fdto7supfzisp1y8.ui.nabu.casa
  Token: override it by env variable HASS_HassOptions__Token
  MaxRetries: 5
  RetryDelayMs: 1000
  StateCheckInterval: every 10 minutes

HvacOptions:
  tempSensor: sensor.1st_floor_hall_multisensor_temperature
  hvacEntities:
    - entityId: climate.living_room_ac
      presetMode: quiet
      enabled: true
      temperature: 23
      defrost: true
    - entityId: climate.bedroom_ac
      enabled: true
      presetMode: windFreeSleep
      temperature: 21
      defrost: false
    - entityId: climate.matej_ac
      enabled: true
      presetMode: windFreeSleep
      temperature: 21
      defrost: false
    - entityId: climate.anicka_ac
      enabled: true
      presetMode: windFreeSleep
      temperature: 21
      defrost: false
    - entityId: climate.radek_ac
      enabled: true
      presetMode: windFreeSleep
      temperature: 21
      defrost: false

  temperatureThresholds:
    min: 19.7
    max: 20.2
    outdoorMin: -5

  defrost:
    temperatureThreshold: 0.0
    periodSeconds: 7200
    durationSeconds: 300

  activeHours:
    start: 8
    startWeekday: 7
    end: 21
