{"rustc": 7501422222776872140, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 4265853119701738230, "profile": 17984201634715228204, "path": 13014822310821566142, "deps": [[953493416998313742, "proc_macro2", false, 15048518836427134279], [6398426461930412821, "quote", false, 922035941187232825], [17852460810103719877, "unicode_ident", false, 13288328555643254513]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-49aa72298baa906d/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}