{"rustc": 7501422222776872140, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 17984201634715228204, "path": 7123167797007192594, "deps": [[5087225350577355032, "cc", false, 15947285416520839466]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ring-b8bebda9e5503003/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}