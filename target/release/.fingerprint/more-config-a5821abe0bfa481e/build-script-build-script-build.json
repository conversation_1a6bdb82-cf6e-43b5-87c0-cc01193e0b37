{"rustc": 7501422222776872140, "features": "[\"async\", \"binder\", \"chained\", \"default\", \"env\", \"std\", \"util\", \"yaml\"]", "declared_features": "[\"all\", \"async\", \"binder\", \"chained\", \"cmd\", \"default\", \"env\", \"ini\", \"json\", \"mem\", \"std\", \"util\", \"xml\", \"yaml\"]", "target": 17883862002600103897, "profile": 17984201634715228204, "path": 17931075413478760514, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/more-config-a5821abe0bfa481e/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}