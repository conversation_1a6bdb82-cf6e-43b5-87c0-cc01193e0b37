cargo:rerun-if-env-changed=RING_PREGENERATE_ASM
cargo:rustc-env=RING_CORE_PREFIX=ring_core_0_17_8_
OPT_LEVEL = Some(3)
OUT_DIR = Some(/home/<USER>/dev/ha/hag/target/x86_64-unknown-linux-gnu/release/build/ring-977276d70e1d664a/out)
TARGET = Some(x86_64-unknown-linux-gnu)
HOST = Some(x86_64-unknown-linux-gnu)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-gnu
CC_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_gnu
CC_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(false)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2,x87)
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-gnu
CFLAGS_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_gnu
CFLAGS_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
CARGO_ENCODED_RUSTFLAGS = Some(-Clink-arg=-fuse-ld=/usr/bin/mold-Zshare-generics=y)
cargo:rustc-link-lib=static=ring_core_0_17_8_
OPT_LEVEL = Some(3)
OUT_DIR = Some(/home/<USER>/dev/ha/hag/target/x86_64-unknown-linux-gnu/release/build/ring-977276d70e1d664a/out)
TARGET = Some(x86_64-unknown-linux-gnu)
HOST = Some(x86_64-unknown-linux-gnu)
cargo:rerun-if-env-changed=CC_x86_64-unknown-linux-gnu
CC_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=CC_x86_64_unknown_linux_gnu
CC_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(false)
CARGO_CFG_TARGET_FEATURE = Some(fxsr,sse,sse2,x87)
cargo:rerun-if-env-changed=CFLAGS_x86_64-unknown-linux-gnu
CFLAGS_x86_64-unknown-linux-gnu = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_unknown_linux_gnu
CFLAGS_x86_64_unknown_linux_gnu = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
CARGO_ENCODED_RUSTFLAGS = Some(-Clink-arg=-fuse-ld=/usr/bin/mold-Zshare-generics=y)
cargo:rustc-link-lib=static=ring_core_0_17_8_test
cargo:rustc-link-search=native=/home/<USER>/dev/ha/hag/target/x86_64-unknown-linux-gnu/release/build/ring-977276d70e1d664a/out
cargo:rerun-if-changed=crypto/constant_time_test.c
cargo:rerun-if-changed=crypto/fipsmodule/modes/asm/aesv8-gcm-armv8.pl
cargo:rerun-if-changed=crypto/fipsmodule/modes/asm/ghash-x86_64.pl
cargo:rerun-if-changed=crypto/fipsmodule/modes/asm/ghash-armv4.pl
cargo:rerun-if-changed=crypto/fipsmodule/modes/asm/ghash-x86.pl
cargo:rerun-if-changed=crypto/fipsmodule/modes/asm/ghashv8-armx.pl
cargo:rerun-if-changed=crypto/fipsmodule/modes/asm/aesni-gcm-x86_64.pl
cargo:rerun-if-changed=crypto/fipsmodule/aes/asm/vpaes-x86_64.pl
cargo:rerun-if-changed=crypto/fipsmodule/aes/asm/aesni-x86.pl
cargo:rerun-if-changed=crypto/fipsmodule/aes/asm/bsaes-armv7.pl
cargo:rerun-if-changed=crypto/fipsmodule/aes/asm/vpaes-x86.pl
cargo:rerun-if-changed=crypto/fipsmodule/aes/asm/aesni-x86_64.pl
cargo:rerun-if-changed=crypto/fipsmodule/aes/asm/aesv8-armx.pl
cargo:rerun-if-changed=crypto/fipsmodule/aes/aes_nohw.c
cargo:rerun-if-changed=crypto/fipsmodule/bn/montgomery_inv.c
cargo:rerun-if-changed=crypto/fipsmodule/bn/asm/x86_64-mont5.pl
cargo:rerun-if-changed=crypto/fipsmodule/bn/asm/x86-mont.pl
cargo:rerun-if-changed=crypto/fipsmodule/bn/asm/x86_64-mont.pl
cargo:rerun-if-changed=crypto/fipsmodule/bn/asm/armv4-mont.pl
cargo:rerun-if-changed=crypto/fipsmodule/bn/asm/armv8-mont.pl
cargo:rerun-if-changed=crypto/fipsmodule/bn/internal.h
cargo:rerun-if-changed=crypto/fipsmodule/bn/montgomery.c
cargo:rerun-if-changed=crypto/fipsmodule/ec/p256-nistz.c
cargo:rerun-if-changed=crypto/fipsmodule/ec/ecp_nistz.c
cargo:rerun-if-changed=crypto/fipsmodule/ec/p256.c
cargo:rerun-if-changed=crypto/fipsmodule/ec/util.h
cargo:rerun-if-changed=crypto/fipsmodule/ec/ecp_nistz384.h
cargo:rerun-if-changed=crypto/fipsmodule/ec/asm/p256-x86_64-asm.pl
cargo:rerun-if-changed=crypto/fipsmodule/ec/asm/p256-armv8-asm.pl
cargo:rerun-if-changed=crypto/fipsmodule/ec/gfp_p256.c
cargo:rerun-if-changed=crypto/fipsmodule/ec/gfp_p384.c
cargo:rerun-if-changed=crypto/fipsmodule/ec/p256_table.h
cargo:rerun-if-changed=crypto/fipsmodule/ec/p256-nistz-table.h
cargo:rerun-if-changed=crypto/fipsmodule/ec/ecp_nistz.h
cargo:rerun-if-changed=crypto/fipsmodule/ec/ecp_nistz384.inl
cargo:rerun-if-changed=crypto/fipsmodule/ec/p256-nistz.h
cargo:rerun-if-changed=crypto/fipsmodule/ec/p256_shared.h
cargo:rerun-if-changed=crypto/fipsmodule/sha/asm/sha256-armv4.pl
cargo:rerun-if-changed=crypto/fipsmodule/sha/asm/sha512-armv4.pl
cargo:rerun-if-changed=crypto/fipsmodule/sha/asm/sha512-armv8.pl
cargo:rerun-if-changed=crypto/fipsmodule/sha/asm/sha512-x86_64.pl
cargo:rerun-if-changed=crypto/mem.c
cargo:rerun-if-changed=crypto/internal.h
cargo:rerun-if-changed=crypto/poly1305/poly1305_arm.c
cargo:rerun-if-changed=crypto/poly1305/internal.h
cargo:rerun-if-changed=crypto/poly1305/poly1305.c
cargo:rerun-if-changed=crypto/poly1305/poly1305_arm_asm.S
cargo:rerun-if-changed=crypto/poly1305/poly1305_vec.c
cargo:rerun-if-changed=crypto/crypto.c
cargo:rerun-if-changed=crypto/cpu_intel.c
cargo:rerun-if-changed=crypto/chacha/asm/chacha-x86_64.pl
cargo:rerun-if-changed=crypto/chacha/asm/chacha-armv8.pl
cargo:rerun-if-changed=crypto/chacha/asm/chacha-armv4.pl
cargo:rerun-if-changed=crypto/chacha/asm/chacha-x86.pl
cargo:rerun-if-changed=crypto/cipher_extra/asm/chacha20_poly1305_armv8.pl
cargo:rerun-if-changed=crypto/cipher_extra/asm/chacha20_poly1305_x86_64.pl
cargo:rerun-if-changed=crypto/perlasm/x86_64-xlate.pl
cargo:rerun-if-changed=crypto/perlasm/x86nasm.pl
cargo:rerun-if-changed=crypto/perlasm/x86gas.pl
cargo:rerun-if-changed=crypto/perlasm/arm-xlate.pl
cargo:rerun-if-changed=crypto/perlasm/x86asm.pl
cargo:rerun-if-changed=crypto/limbs/limbs.c
cargo:rerun-if-changed=crypto/limbs/limbs.h
cargo:rerun-if-changed=crypto/limbs/limbs.inl
cargo:rerun-if-changed=crypto/curve25519/curve25519_64_adx.c
cargo:rerun-if-changed=crypto/curve25519/asm/x25519-asm-arm.S
cargo:rerun-if-changed=crypto/curve25519/internal.h
cargo:rerun-if-changed=crypto/curve25519/curve25519_tables.h
cargo:rerun-if-changed=crypto/curve25519/curve25519.c
cargo:rerun-if-changed=include/ring-core/mem.h
cargo:rerun-if-changed=include/ring-core/type_check.h
cargo:rerun-if-changed=include/ring-core/base.h
cargo:rerun-if-changed=include/ring-core/check.h
cargo:rerun-if-changed=include/ring-core/arm_arch.h
cargo:rerun-if-changed=include/ring-core/asm_base.h
cargo:rerun-if-changed=include/ring-core/target.h
cargo:rerun-if-changed=include/ring-core/poly1305.h
cargo:rerun-if-changed=include/ring-core/aes.h
cargo:rerun-if-changed=third_party/fiat/curve25519_64.h
cargo:rerun-if-changed=third_party/fiat/curve25519_32.h
cargo:rerun-if-changed=third_party/fiat/curve25519_64_msvc.h
cargo:rerun-if-changed=third_party/fiat/p256_64.h
cargo:rerun-if-changed=third_party/fiat/curve25519_64_adx.h
cargo:rerun-if-changed=third_party/fiat/asm/fiat_curve25519_adx_square.S
cargo:rerun-if-changed=third_party/fiat/asm/fiat_curve25519_adx_mul.S
cargo:rerun-if-changed=third_party/fiat/LICENSE
cargo:rerun-if-changed=third_party/fiat/p256_64_msvc.h
cargo:rerun-if-changed=third_party/fiat/p256_32.h
