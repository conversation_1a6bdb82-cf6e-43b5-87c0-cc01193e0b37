{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4468123440088164316, "build_script_build", false, 16734891347392020978]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/release/build/crossbeam-utils-d98360ec4871740d/output", "paths": ["no_atomic.rs"]}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}