{"rustc": 7501422222776872140, "features": "[\"default\", \"english\", \"english-to-cron\"]", "declared_features": "[\"async-nats\", \"bytes\", \"default\", \"english\", \"english-to-cron\", \"has_bytes\", \"log\", \"nats_storage\", \"postgres-native-tls\", \"postgres-openssl\", \"postgres_native_tls\", \"postgres_openssl\", \"postgres_storage\", \"prost\", \"prost-build\", \"signal\", \"tokio-postgres\", \"tracing-subscriber\"]", "target": 8730579494265005221, "profile": 5676177281124120482, "path": 9395774266481314491, "deps": [[2244687516443770123, "uuid", false, 2318421100019799172], [5157631553186200874, "num_traits", false, 4759878894181935978], [6377879556277388403, "chrono", false, 5323007106178140783], [8606274917505247608, "tracing", false, 15457948468706444014], [9538054652646069845, "tokio", false, 3912783559472812977], [9617123761640566343, "build_script_build", false, 12747074510680095556], [11263754829263059703, "num_derive", false, 16236665504412869911], [12188674108706260368, "english_to_cron", false, 18203433034751238583], [12608297463642269925, "croner", false, 16141984684500024419]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/tokio-cron-scheduler-300b360c08963667/dep-lib-tokio_cron_scheduler", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}