{"rustc": 7501422222776872140, "features": "[\"connect\", \"default\", \"handshake\", \"stream\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"url\", \"webpki-roots\"]", "target": 10194999948271016277, "profile": 5676177281124120482, "path": 15619716395410849403, "deps": [[385810070298638530, "log", false, 8484069575167732481], [722540425891702432, "tungstenite", false, 9668999976847452498], [9538054652646069845, "tokio", false, 3912783559472812977], [10629569228670356391, "futures_util", false, 612132832086782614]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/tokio-tungstenite-c9ef008791db23c0/dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}