{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"rustc-dep-of-std\"]", "target": 14691992093392644261, "profile": 5676177281124120482, "path": 9221898296112111852, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/cfg-if-29660de587cd7fa8/dep-lib-cfg_if", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}