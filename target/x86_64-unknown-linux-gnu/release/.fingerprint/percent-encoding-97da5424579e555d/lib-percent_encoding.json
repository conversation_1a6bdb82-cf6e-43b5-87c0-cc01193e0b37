{"rustc": 7501422222776872140, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 13121965682019140665, "profile": 5676177281124120482, "path": 14519584606101931009, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/percent-encoding-97da5424579e555d/dep-lib-percent_encoding", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}