{"rustc": 7501422222776872140, "features": "[\"default\", \"once_cell\", \"std\"]", "declared_features": "[\"default\", \"once_cell\", \"std\", \"valuable\"]", "target": 14276081467424924844, "profile": 3541797763817303166, "path": 15264319560705084578, "deps": [[15777851237800602919, "once_cell", false, 10448291693403668207]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/tracing-core-453e3112f5f0c262/dep-lib-tracing_core", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}