{"rustc": 7501422222776872140, "features": "[\"unicode-case\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5676177281124120482, "path": 3278722190966782547, "deps": [[555019317135488525, "regex_automata", false, 3325854724956396718], [9408802513701742484, "regex_syntax", false, 10945068458806324332]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/regex-09c53fd59ceafcc0/dep-lib-regex", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}