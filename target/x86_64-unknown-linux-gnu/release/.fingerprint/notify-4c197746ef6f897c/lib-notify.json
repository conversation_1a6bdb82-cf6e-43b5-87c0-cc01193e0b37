{"rustc": 7501422222776872140, "features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"macos_fsevent\"]", "declared_features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"kqueue\", \"macos_fsevent\", \"macos_kqueue\", \"manual_tests\", \"mio\", \"serde\", \"timing_tests\"]", "target": 4487759779636071210, "profile": 5676177281124120482, "path": 3702660677779115990, "deps": [[385810070298638530, "log", false, 8484069575167732481], [3869670940427635694, "filetime", false, 786826781320980632], [9559541369283268958, "libc", false, 10602105674451268910], [10703860158168350592, "mio", false, 17154668218873109418], [10862151226618880473, "crossbeam_channel", false, 10433917270031284456], [15622660310229662834, "walkdir", false, 17866608996974601578], [16494014898371841369, "inotify", false, 15530526231123720419]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/notify-4c197746ef6f897c/dep-lib-notify", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}