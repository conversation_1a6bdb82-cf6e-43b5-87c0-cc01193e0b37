{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11568354178493685438, "build_script_build", false, 6870418298266570033]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/release/build/parking_lot_core-1b4e4b7b4f702713/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}