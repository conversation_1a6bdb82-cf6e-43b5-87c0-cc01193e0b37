{"rustc": 7501422222776872140, "features": "[\"const_generics\", \"const_new\"]", "declared_features": "[\"arbitrary\", \"const_generics\", \"const_new\", \"debugger_visualizer\", \"drain_filter\", \"drain_keep_rest\", \"may_dangle\", \"serde\", \"specialization\", \"union\", \"write\"]", "target": 17147953314976209329, "profile": 5676177281124120482, "path": 13390662796721396134, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/smallvec-93cf86e052c0fd75/dep-lib-smallvec", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}