{"rustc": 7501422222776872140, "features": "[\"default\", \"rust_1_61\", \"rust_1_65\", \"std\"]", "declared_features": "[\"backtrace\", \"backtraces-impl-backtrace-crate\", \"default\", \"futures\", \"futures-core-crate\", \"futures-crate\", \"guide\", \"internal-dev-dependencies\", \"pin-project\", \"rust_1_61\", \"rust_1_65\", \"rust_1_81\", \"std\", \"unstable-core-error\", \"unstable-provider-api\", \"unstable-try-trait\"]", "target": 3864378442534239388, "profile": 5676177281124120482, "path": 16072701457481468540, "deps": [[4465051687875647259, "snafu_derive", false, 1011343014406136393]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/snafu-94033c256ac27817/dep-lib-snafu", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}