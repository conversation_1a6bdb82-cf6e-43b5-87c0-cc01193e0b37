{"rustc": 7501422222776872140, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 5532804495822580834, "path": 6467252718112229976, "deps": [[40386456601120721, "percent_encoding", false, 10209130996830712022], [95042085696191081, "ipnet", false, 3276492071955771513], [385810070298638530, "log", false, 8484069575167732481], [411067296443658118, "serde", false, 12488603973225479114], [784494742817713399, "tower_service", false, 3196159576333548828], [828094305214142069, "http", false, 3086188299011150284], [1791586607557829099, "serde_json", false, 10831119351132801710], [1906322745568073236, "pin_project_lite", false, 4290949107245600688], [2517136641825875337, "sync_wrapper", false, 16250519704852690478], [3150220818285335163, "url", false, 15839958799549239704], [3319604807018780839, "hyper_rustls", false, 2004508497009681515], [4920660634395069245, "hyper_util", false, 17823907831074870641], [4958358643993419201, "rustls", false, 12561511394373397594], [5695049318159433696, "tower", false, 14711001454800871299], [7489145127516980061, "bytes", false, 9487642207639908164], [7620660491849607393, "futures_core", false, 5784098897397484881], [9538054652646069845, "tokio", false, 3912783559472812977], [10036721834787556336, "http_body_util", false, 16235155907886922034], [10229185211513642314, "mime", false, 5998211622478150663], [10629569228670356391, "futures_util", false, 612132832086782614], [11957360342995674422, "hyper", false, 12052631139673577045], [12775413451309404286, "tokio_rustls", false, 12871630716071335145], [13077212702700853852, "base64", false, 8696042786190474854], [14084095096285906100, "http_body", false, 14856542738399478660], [15032952994102373905, "rustls_pemfile", false, 6229651122063275057], [15594963368141592132, "webpki_roots", false, 13245675250562884849], [15777851237800602919, "once_cell", false, 10448291693403668207], [16009134724182327169, "rustls_pki_types", false, 13133558057289253885], [16542808166767769916, "serde_urlencoded", false, 11179812496027453930]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/reqwest-f1d7c1f40a4c9022/dep-lib-reqwest", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}