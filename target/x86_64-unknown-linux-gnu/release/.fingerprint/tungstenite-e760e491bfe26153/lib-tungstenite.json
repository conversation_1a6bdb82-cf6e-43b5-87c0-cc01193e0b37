{"rustc": 7501422222776872140, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 5395530797274129873, "profile": 5676177281124120482, "path": 13221412827956651073, "deps": [[385810070298638530, "log", false, 8484069575167732481], [828094305214142069, "http", false, 3086188299011150284], [2452088868158763674, "thiserror", false, 9207871284696176255], [3712811570531045576, "byteorder", false, 4437565041973535278], [4359956005902820838, "utf8", false, 15088002419668785150], [7489145127516980061, "bytes", false, 9487642207639908164], [8824002584509796034, "data_encoding", false, 10468406355130626138], [9632710759077199072, "httparse", false, 8722081603071247025], [10724389056617919257, "sha1", false, 7255443709836523585], [13208667028893622512, "rand", false, 11075662197086683131]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/tungstenite-e760e491bfe26153/dep-lib-tungstenite", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}