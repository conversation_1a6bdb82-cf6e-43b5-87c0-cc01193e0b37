{"rustc": 7501422222776872140, "features": "[\"alloc\", \"block-buffer\", \"core-api\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"blobby\", \"block-buffer\", \"const-oid\", \"core-api\", \"default\", \"dev\", \"mac\", \"oid\", \"rand_core\", \"std\", \"subtle\"]", "target": 7510122432137863311, "profile": 5676177281124120482, "path": 6754346754169480127, "deps": [[2352660017780662552, "crypto_common", false, 16826092025506039478], [10626340395483396037, "block_buffer", false, 1581049319464941741]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/digest-e2f7a88a9b9a49e8/dep-lib-digest", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}