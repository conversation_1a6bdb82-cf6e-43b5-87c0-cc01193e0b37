{"rustc": 7501422222776872140, "features": "[\"async\", \"binder\", \"chained\", \"default\", \"env\", \"std\", \"util\", \"yaml\"]", "declared_features": "[\"all\", \"async\", \"binder\", \"chained\", \"cmd\", \"default\", \"env\", \"ini\", \"json\", \"mem\", \"std\", \"util\", \"xml\", \"yaml\"]", "target": 4582953372478289379, "profile": 5676177281124120482, "path": 103429922765929553, "deps": [[411067296443658118, "serde", false, 12488603973225479114], [1173784911866891520, "build_script_build", false, 2273825547639242710], [9533228976435881930, "tokens", false, 16070680454784602379], [9614479274285663593, "serde_yaml", false, 1672529774872718467], [10411997081178400487, "cfg_if", false, 1767235018570872151]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/more-config-66571d99e0a80a58/dep-lib-config", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}