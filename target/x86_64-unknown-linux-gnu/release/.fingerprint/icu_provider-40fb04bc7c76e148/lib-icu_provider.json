{"rustc": 7501422222776872140, "features": "[\"macros\"]", "declared_features": "[\"bench\", \"datagen\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"experimental\", \"log_error_context\", \"logging\", \"macros\", \"serde\", \"std\", \"sync\"]", "target": *******************, "profile": 5676177281124120482, "path": 17836402260949316459, "deps": [[1218499983858120347, "icu_locid", false, 2258050848654554090], [*******************, "stable_deref_trait", false, 3334700231160957307], [5298260564258778412, "displaydoc", false, 320240310055351904], [6573907571211658978, "zerofrom", false, 8398522038899144749], [8375043423015247637, "tinystr", false, ******************], [11734826517668253792, "writeable", false, 2920055256751031402], [14072360194315679348, "zerovec", false, 7345550411861112775], [15837516966853249478, "icu_provider_macros", false, 3284805005265760295], [17505694197072478820, "yoke", false, 12535445057262302372]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/icu_provider-40fb04bc7c76e148/dep-lib-icu_provider", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}