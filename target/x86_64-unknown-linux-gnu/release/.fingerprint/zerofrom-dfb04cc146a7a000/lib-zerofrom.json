{"rustc": 7501422222776872140, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 5676177281124120482, "path": 15658576797223435497, "deps": [[10069622712524343299, "zerofrom_derive", false, 16190281434566700101]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/zerofrom-dfb04cc146a7a000/dep-lib-zerofrom", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}