{"rustc": 7501422222776872140, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"arrayvec\", \"smallvec\"]", "target": 978619995687283540, "profile": 5676177281124120482, "path": 926128579602250475, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/write16-42f361994795b1df/dep-lib-write16", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}