{"rustc": 7501422222776872140, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 9642064906272707285, "path": 5498406861360595610, "deps": [[724804171976944018, "num_conv", false, 15693048960567895661], [4880290578780516359, "num_threads", false, 16988465876502732345], [5901133744777009488, "powerfmt", false, 11062759537424585685], [9272993696668697301, "itoa", false, 15474583067178340382], [9559541369283268958, "libc", false, 10602105674451268910], [9963315149517826755, "deranged", false, 6031892478132665858], [11984954412579621937, "time_macros", false, 12023935900078922454], [14288011715199747663, "time_core", false, 9770918388075648257]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/time-d40bdbf7f689df8c/dep-lib-time", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}