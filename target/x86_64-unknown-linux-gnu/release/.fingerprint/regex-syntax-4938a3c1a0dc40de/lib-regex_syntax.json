{"rustc": 7501422222776872140, "features": "[\"unicode-case\"]", "declared_features": "[\"arbitrary\", \"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 742186494246220192, "profile": 5676177281124120482, "path": 10888788854618341831, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/regex-syntax-4938a3c1a0dc40de/dep-lib-regex_syntax", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}