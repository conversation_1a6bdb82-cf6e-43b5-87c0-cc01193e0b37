{"rustc": 7501422222776872140, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws_lc_rs\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 5676177281124120482, "path": 4246214843606832768, "deps": [[507245148068099358, "ring", false, 5650319800313337332], [8995469080876806959, "untrusted", false, 10897601187871341720], [16009134724182327169, "pki_types", false, 13133558057289253885]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/rustls-webpki-e1eae55a595c38f2/dep-lib-webpki", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}