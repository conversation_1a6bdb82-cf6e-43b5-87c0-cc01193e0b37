{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[\"default\", \"use_std\"]", "target": 3556356971060988614, "profile": 5676177281124120482, "path": 17812472620518160278, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/scopeguard-7e2547863f2919aa/dep-lib-scopeguard", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}