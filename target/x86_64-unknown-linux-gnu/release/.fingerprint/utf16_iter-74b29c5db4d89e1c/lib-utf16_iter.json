{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[]", "target": 9006113137700540147, "profile": 5676177281124120482, "path": 7595493623500437787, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/utf16_iter-74b29c5db4d89e1c/dep-lib-utf16_iter", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}