{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8722757871864480630, "build_script_main", false, 5511114229393826629]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/release/build/typenum-8ff9030d5f8c6426/output", "paths": ["build/main.rs"]}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}