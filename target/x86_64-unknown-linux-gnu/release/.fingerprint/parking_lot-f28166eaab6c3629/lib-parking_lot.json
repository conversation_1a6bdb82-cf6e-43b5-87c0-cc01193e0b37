{"rustc": 7501422222776872140, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"hardware-lock-elision\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\"]", "target": 9887373948397848517, "profile": 5676177281124120482, "path": 7047403782597450027, "deps": [[4796557380116975802, "lock_api", false, 15874571590639956789], [11568354178493685438, "parking_lot_core", false, 3057214275598523300]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/parking_lot-f28166eaab6c3629/dep-lib-parking_lot", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}