{"rustc": 7501422222776872140, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18134297140301713016, "path": 11948470764360580879, "deps": [[5103565458935487, "futures_io", false, 9432581455555379758], [1615478164327904835, "pin_utils", false, 9116357246173810317], [1811549171721445101, "futures_channel", false, 17018814901936942670], [1906322745568073236, "pin_project_lite", false, 4290949107245600688], [3129130049864710036, "memchr", false, 7116079029440813623], [6955678925937229351, "slab", false, 11386859587845849280], [7013762810557009322, "futures_sink", false, 16419238314678184117], [7620660491849607393, "futures_core", false, 5784098897397484881], [10565019901765856648, "futures_macro", false, 2731810213909147286], [16240732885093539806, "futures_task", false, 5704373133847503358]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/futures-util-bde761b0e765e6f5/dep-lib-futures_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}