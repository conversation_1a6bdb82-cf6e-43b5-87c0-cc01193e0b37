{"rustc": 7501422222776872140, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5676177281124120482, "path": 12533253869799718830, "deps": [[507245148068099358, "ring", false, 5650319800313337332], [4958358643993419201, "build_script_build", false, 4894312716427542707], [6528079939221783635, "zeroize", false, 2701045239475312314], [12989347533245466967, "<PERSON><PERSON><PERSON>", false, 570451858407869828], [15777851237800602919, "once_cell", false, 10448291693403668207], [16009134724182327169, "pki_types", false, 13133558057289253885], [17003143334332120809, "subtle", false, 529325332574595633]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/rustls-a8fbfd7bf2c9d7e7/dep-lib-rustls", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}