{"rustc": 7501422222776872140, "features": "[\"alloc\", \"zerovec\"]", "declared_features": "[\"alloc\", \"bench\", \"databake\", \"default\", \"serde\", \"std\", \"zerovec\"]", "target": 13673663638038720539, "profile": 5676177281124120482, "path": 8433361759562965503, "deps": [[5298260564258778412, "displaydoc", false, 320240310055351904], [14072360194315679348, "zerovec", false, 7345550411861112775]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/tinystr-8a7a59bd41a020e4/dep-lib-tinystr", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}