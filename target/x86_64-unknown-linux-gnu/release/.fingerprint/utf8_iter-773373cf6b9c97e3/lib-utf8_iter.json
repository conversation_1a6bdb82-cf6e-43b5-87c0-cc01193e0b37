{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[]", "target": 6216520282702351879, "profile": 5676177281124120482, "path": 17665540980259184049, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/utf8_iter-773373cf6b9c97e3/dep-lib-utf8_iter", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}