{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[\"rustc-dep-of-std\", \"std\", \"wasm_js\"]", "target": 11669924403970522481, "profile": 10139720588991534553, "path": 16827875634720788215, "deps": [[9559541369283268958, "libc", false, 10602105674451268910], [10411997081178400487, "cfg_if", false, 1767235018570872151], [12546824318118147037, "build_script_build", false, 16916451728757843749]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/getrandom-edd12eb000f927fd/dep-lib-getrandom", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}