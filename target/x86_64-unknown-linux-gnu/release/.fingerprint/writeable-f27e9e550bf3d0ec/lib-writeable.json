{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[\"bench\", \"either\"]", "target": 2501750852737614978, "profile": 5676177281124120482, "path": 6429000989009562501, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/writeable-f27e9e550bf3d0ec/dep-lib-writeable", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}