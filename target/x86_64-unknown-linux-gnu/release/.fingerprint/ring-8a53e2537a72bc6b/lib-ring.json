{"rustc": 7501422222776872140, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 5676177281124120482, "path": 4590293954615689061, "deps": [[507245148068099358, "build_script_build", false, 6670346837261741682], [2313368913568865230, "spin", false, 2219846660696420904], [7670211519503158651, "getrandom", false, 8204587739354133413], [8995469080876806959, "untrusted", false, 10897601187871341720], [10411997081178400487, "cfg_if", false, 1767235018570872151]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/ring-8a53e2537a72bc6b/dep-lib-ring", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}