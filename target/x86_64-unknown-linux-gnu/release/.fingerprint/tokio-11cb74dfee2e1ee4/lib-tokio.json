{"rustc": 7501422222776872140, "features": "[\"bytes\", \"default\", \"io-util\", \"libc\", \"macros\", \"mio\", \"net\", \"rt\", \"rt-multi-thread\", \"signal\", \"signal-hook-registry\", \"socket2\", \"sync\", \"time\", \"tokio-macros\", \"tracing\"]", "declared_features": "[\"bytes\", \"default\", \"fs\", \"full\", \"io-std\", \"io-util\", \"libc\", \"macros\", \"mio\", \"net\", \"parking_lot\", \"process\", \"rt\", \"rt-multi-thread\", \"signal\", \"signal-hook-registry\", \"socket2\", \"sync\", \"test-util\", \"time\", \"tokio-macros\", \"tracing\", \"windows-sys\"]", "target": 9605832425414080464, "profile": 13488188181128418296, "path": 13542717102276844519, "deps": [[1208444357745175282, "mio", false, 11676504730507982169], [1812404384583366124, "tokio_macros", false, 7594940038111902992], [1906322745568073236, "pin_project_lite", false, 4290949107245600688], [3264248519883990135, "socket2", false, 4427905740623248521], [7489145127516980061, "bytes", false, 9487642207639908164], [9261933396201778893, "signal_hook_registry", false, 6468870749579347249], [9559541369283268958, "libc", false, 10602105674451268910]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/tokio-11cb74dfee2e1ee4/dep-lib-tokio", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}