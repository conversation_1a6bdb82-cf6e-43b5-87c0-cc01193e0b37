{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 977412477560733980, "profile": 5676177281124120482, "path": 462229141814473659, "deps": [[6831611227313043439, "smallvec", false, 6593534314368433237], [9559541369283268958, "libc", false, 10602105674451268910], [10411997081178400487, "cfg_if", false, 1767235018570872151], [11568354178493685438, "build_script_build", false, 9250921021193366219]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/parking_lot_core-26cf27aaa02dc502/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}