{"rustc": 7501422222776872140, "features": "[\"async\", \"cfg\", \"di\", \"maybe-impl\", \"more-config\", \"more-di\", \"serde\"]", "declared_features": "[\"async\", \"cfg\", \"di\", \"maybe-impl\", \"more-config\", \"more-di\", \"serde\"]", "target": 4022151879802144449, "profile": 5676177281124120482, "path": 6964573866229607990, "deps": [[411067296443658118, "serde", false, 12488603973225479114], [1173784911866891520, "config", false, 686993795439087059], [3240539862600496945, "maybe_impl", false, 12301588955780656524], [7986280626685324278, "di", false, 12994623142909296450], [9533228976435881930, "tokens", false, 16070680454784602379]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/more-options-58aa5a21eb160cd9/dep-lib-options", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}