{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[]", "target": 15336014112896923638, "profile": 5676177281124120482, "path": 8548753051900538454, "deps": [[411067296443658118, "serde", false, 12488603973225479114], [1173784911866891520, "config", false, 686993795439087059], [1791586607557829099, "serde_json", false, 10831119351132801710], [2062481783838671931, "parking_lot", false, 14471580027655449997], [6280640092497431962, "options", false, 4644573095414055725], [7228062143122072479, "kameo_actors", false, 13560621073215223140], [7986280626685324278, "di", false, 12994623142909296450], [8606274917505247608, "tracing", false, 15457948468706444014], [9538054652646069845, "tokio", false, 3912783559472812977], [9614479274285663593, "serde_yaml", false, 1672529774872718467], [9617123761640566343, "tokio_cron_scheduler", false, 8611002585665873037], [9813213285881231547, "reqwest", false, 6771617347096596103], [10525753287504657187, "time", false, 905358512381525626], [11026273334807506675, "snafu", false, 7167161589848528775], [11736783209744844508, "hass_rs", false, 15108364827119218898], [13787082801403653804, "async_trait", false, 9758760233080158836], [16230660778393187092, "tracing_subscriber", false, 679136552944480847], [17392930845109885325, "kameo", false, 1262751072080646599]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/hag-core-f0ada056a0b96288/dep-lib-hag_core", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}