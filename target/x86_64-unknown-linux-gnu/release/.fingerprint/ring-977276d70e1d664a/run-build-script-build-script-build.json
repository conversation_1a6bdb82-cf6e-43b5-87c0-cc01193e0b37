{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[507245148068099358, "build_script_build", false, 7301569036420873944]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/release/build/ring-977276d70e1d664a/output", "paths": ["crypto/constant_time_test.c", "crypto/fipsmodule/modes/asm/aesv8-gcm-armv8.pl", "crypto/fipsmodule/modes/asm/ghash-x86_64.pl", "crypto/fipsmodule/modes/asm/ghash-armv4.pl", "crypto/fipsmodule/modes/asm/ghash-x86.pl", "crypto/fipsmodule/modes/asm/ghashv8-armx.pl", "crypto/fipsmodule/modes/asm/aesni-gcm-x86_64.pl", "crypto/fipsmodule/aes/asm/vpaes-x86_64.pl", "crypto/fipsmodule/aes/asm/aesni-x86.pl", "crypto/fipsmodule/aes/asm/bsaes-armv7.pl", "crypto/fipsmodule/aes/asm/vpaes-x86.pl", "crypto/fipsmodule/aes/asm/aesni-x86_64.pl", "crypto/fipsmodule/aes/asm/aesv8-armx.pl", "crypto/fipsmodule/aes/aes_nohw.c", "crypto/fipsmodule/bn/montgomery_inv.c", "crypto/fipsmodule/bn/asm/x86_64-mont5.pl", "crypto/fipsmodule/bn/asm/x86-mont.pl", "crypto/fipsmodule/bn/asm/x86_64-mont.pl", "crypto/fipsmodule/bn/asm/armv4-mont.pl", "crypto/fipsmodule/bn/asm/armv8-mont.pl", "crypto/fipsmodule/bn/internal.h", "crypto/fipsmodule/bn/montgomery.c", "crypto/fipsmodule/ec/p256-nistz.c", "crypto/fipsmodule/ec/ecp_nistz.c", "crypto/fipsmodule/ec/p256.c", "crypto/fipsmodule/ec/util.h", "crypto/fipsmodule/ec/ecp_nistz384.h", "crypto/fipsmodule/ec/asm/p256-x86_64-asm.pl", "crypto/fipsmodule/ec/asm/p256-armv8-asm.pl", "crypto/fipsmodule/ec/gfp_p256.c", "crypto/fipsmodule/ec/gfp_p384.c", "crypto/fipsmodule/ec/p256_table.h", "crypto/fipsmodule/ec/p256-nistz-table.h", "crypto/fipsmodule/ec/ecp_nistz.h", "crypto/fipsmodule/ec/ecp_nistz384.inl", "crypto/fipsmodule/ec/p256-nistz.h", "crypto/fipsmodule/ec/p256_shared.h", "crypto/fipsmodule/sha/asm/sha256-armv4.pl", "crypto/fipsmodule/sha/asm/sha512-armv4.pl", "crypto/fipsmodule/sha/asm/sha512-armv8.pl", "crypto/fipsmodule/sha/asm/sha512-x86_64.pl", "crypto/mem.c", "crypto/internal.h", "crypto/poly1305/poly1305_arm.c", "crypto/poly1305/internal.h", "crypto/poly1305/poly1305.c", "crypto/poly1305/poly1305_arm_asm.S", "crypto/poly1305/poly1305_vec.c", "crypto/crypto.c", "crypto/cpu_intel.c", "crypto/chacha/asm/chacha-x86_64.pl", "crypto/chacha/asm/chacha-armv8.pl", "crypto/chacha/asm/chacha-armv4.pl", "crypto/chacha/asm/chacha-x86.pl", "crypto/cipher_extra/asm/chacha20_poly1305_armv8.pl", "crypto/cipher_extra/asm/chacha20_poly1305_x86_64.pl", "crypto/perlasm/x86_64-xlate.pl", "crypto/perlasm/x86nasm.pl", "crypto/perlasm/x86gas.pl", "crypto/perlasm/arm-xlate.pl", "crypto/perlasm/x86asm.pl", "crypto/limbs/limbs.c", "crypto/limbs/limbs.h", "crypto/limbs/limbs.inl", "crypto/curve25519/curve25519_64_adx.c", "crypto/curve25519/asm/x25519-asm-arm.S", "crypto/curve25519/internal.h", "crypto/curve25519/curve25519_tables.h", "crypto/curve25519/curve25519.c", "include/ring-core/mem.h", "include/ring-core/type_check.h", "include/ring-core/base.h", "include/ring-core/check.h", "include/ring-core/arm_arch.h", "include/ring-core/asm_base.h", "include/ring-core/target.h", "include/ring-core/poly1305.h", "include/ring-core/aes.h", "third_party/fiat/curve25519_64.h", "third_party/fiat/curve25519_32.h", "third_party/fiat/curve25519_64_msvc.h", "third_party/fiat/p256_64.h", "third_party/fiat/curve25519_64_adx.h", "third_party/fiat/asm/fiat_curve25519_adx_square.S", "third_party/fiat/asm/fiat_curve25519_adx_mul.S", "third_party/fiat/LICENSE", "third_party/fiat/p256_64_msvc.h", "third_party/fiat/p256_32.h"]}}, {"RerunIfEnvChanged": {"var": "RING_PREGENERATE_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}