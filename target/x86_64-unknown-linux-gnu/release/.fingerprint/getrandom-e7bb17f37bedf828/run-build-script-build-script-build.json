{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12546824318118147037, "build_script_build", false, 18116775428779348642]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/release/build/getrandom-e7bb17f37bedf828/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}