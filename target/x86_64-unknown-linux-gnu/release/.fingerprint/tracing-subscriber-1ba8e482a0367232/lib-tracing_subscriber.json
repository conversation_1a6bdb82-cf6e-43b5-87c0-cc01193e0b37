{"rustc": 7501422222776872140, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"local-time\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3541797763817303166, "path": 13632755323054943515, "deps": [[1017461770342116999, "sharded_slab", false, 9106587131997895829], [6831611227313043439, "smallvec", false, 6593534314368433237], [8614575489689151157, "nu_ansi_term", false, 16948280920044812835], [10525753287504657187, "time", false, 905358512381525626], [10806489435541507125, "tracing_log", false, 10422157208426038135], [11033263105862272874, "tracing_core", false, 2417955971672961468], [12427285511609802057, "thread_local", false, 16978168710758981477]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/tracing-subscriber-1ba8e482a0367232/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}