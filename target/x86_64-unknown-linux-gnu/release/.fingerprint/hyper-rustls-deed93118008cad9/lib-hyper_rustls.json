{"rustc": 7501422222776872140, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 5676177281124120482, "path": 6686354174269659521, "deps": [[784494742817713399, "tower_service", false, 3196159576333548828], [828094305214142069, "http", false, 3086188299011150284], [4920660634395069245, "hyper_util", false, 17823907831074870641], [4958358643993419201, "rustls", false, 12561511394373397594], [9538054652646069845, "tokio", false, 3912783559472812977], [10629569228670356391, "futures_util", false, 612132832086782614], [11957360342995674422, "hyper", false, 12052631139673577045], [12775413451309404286, "tokio_rustls", false, 12871630716071335145], [15594963368141592132, "webpki_roots", false, 13245675250562884849], [16009134724182327169, "pki_types", false, 13133558057289253885]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/hyper-rustls-deed93118008cad9/dep-lib-hyper_rustls", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}