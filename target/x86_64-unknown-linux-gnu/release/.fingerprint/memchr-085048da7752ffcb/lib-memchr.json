{"rustc": 7501422222776872140, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"compiler_builtins\", \"core\", \"default\", \"libc\", \"logging\", \"rustc-dep-of-std\", \"std\", \"use_std\"]", "target": 11745930252914242013, "profile": 5676177281124120482, "path": 13955998482447071740, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/memchr-085048da7752ffcb/dep-lib-memchr", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}