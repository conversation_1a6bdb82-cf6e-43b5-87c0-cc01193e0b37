{"rustc": 7501422222776872140, "features": "[\"client\", \"client-legacy\", \"default\", \"http1\", \"tokio\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"client\", \"client-legacy\", \"default\", \"full\", \"http1\", \"http2\", \"server\", \"server-auto\", \"server-graceful\", \"service\", \"tokio\"]", "target": 11100538814903412163, "profile": 5676177281124120482, "path": 8873410972515250375, "deps": [[784494742817713399, "tower_service", false, 3196159576333548828], [828094305214142069, "http", false, 3086188299011150284], [1811549171721445101, "futures_channel", false, 17018814901936942670], [1906322745568073236, "pin_project_lite", false, 4290949107245600688], [3264248519883990135, "socket2", false, 4427905740623248521], [7489145127516980061, "bytes", false, 9487642207639908164], [8606274917505247608, "tracing", false, 15457948468706444014], [9538054652646069845, "tokio", false, 3912783559472812977], [10629569228670356391, "futures_util", false, 612132832086782614], [11957360342995674422, "hyper", false, 12052631139673577045], [14084095096285906100, "http_body", false, 14856542738399478660]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/hyper-util-fa97fe526965e3aa/dep-lib-hyper_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}