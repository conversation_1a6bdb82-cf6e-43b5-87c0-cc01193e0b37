{"rustc": 7501422222776872140, "features": "[\"default\", \"derive\", \"serde_derive\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"derive\", \"rc\", \"serde_derive\", \"std\", \"unstable\"]", "target": 4017624490652051717, "profile": 5676177281124120482, "path": 15375945458217290360, "deps": [[411067296443658118, "build_script_build", false, 10081841793325101104], [5064123231203445723, "serde_derive", false, 12751753233676683564]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/serde-b6f412523efa9d19/dep-lib-serde", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}