{"rustc": 7501422222776872140, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"aarch64\", \"alloc\", \"default\", \"derive\", \"serde\", \"simd\", \"std\", \"zeroize_derive\"]", "target": 12572013220049634676, "profile": 5676177281124120482, "path": 1458142056660352952, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/zeroize-835590ab41cdfd7b/dep-lib-zeroize", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}