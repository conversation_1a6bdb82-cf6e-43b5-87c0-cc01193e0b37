{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1791586607557829099, "build_script_build", false, 718899096657629091]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/release/build/serde_json-ffc9ed2d1595c637/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}