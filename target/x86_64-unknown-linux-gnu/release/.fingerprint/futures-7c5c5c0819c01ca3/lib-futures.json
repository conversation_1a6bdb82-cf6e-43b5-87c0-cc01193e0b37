{"rustc": 7501422222776872140, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18134297140301713016, "path": 9874546048964616035, "deps": [[5103565458935487, "futures_io", false, 9432581455555379758], [1811549171721445101, "futures_channel", false, 17018814901936942670], [7013762810557009322, "futures_sink", false, 16419238314678184117], [7620660491849607393, "futures_core", false, 5784098897397484881], [10629569228670356391, "futures_util", false, 612132832086782614], [12779779637805422465, "futures_executor", false, 14550314569846513774], [16240732885093539806, "futures_task", false, 5704373133847503358]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/release/.fingerprint/futures-7c5c5c0819c01ca3/dep-lib-futures", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}