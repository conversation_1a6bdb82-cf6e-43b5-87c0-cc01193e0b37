{"rustc": 7501422222776872140, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cfg-target-has-atomic\", \"default\", \"portable-atomic\", \"std\", \"unstable\"]", "target": 9453135960607436725, "profile": 10452971554943060093, "path": 10497841096174693576, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-core-3dbdfcab523b4835/dep-lib-futures_core", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}