{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[]", "target": 1347177389841305098, "profile": 8390198099952646979, "path": 14672471563019832539, "deps": [[411067296443658118, "serde", false, 2836415886721349815], [1173784911866891520, "config", false, 1508704611228094731], [1791586607557829099, "serde_json", false, 582705126220715569], [1931345177571435668, "hag_core", false, 15982627741235857561], [2062481783838671931, "parking_lot", false, 12717460915451027645], [6280640092497431962, "options", false, 5987403653540039937], [6612275129670119762, "smlang", false, 14342434425842748746], [7228062143122072479, "kameo_actors", false, 13910864296247229276], [7986280626685324278, "di", false, 9266093473503408154], [8606274917505247608, "tracing", false, 1775809060611537187], [9538054652646069845, "tokio", false, 5241801424725673318], [9614479274285663593, "serde_yaml", false, 3765926043665047641], [10525753287504657187, "time", false, 17521956304773683034], [11026273334807506675, "snafu", false, 10199197144313684281], [11736783209744844508, "hass_rs", false, 13878484065021067202], [13787082801403653804, "async_trait", false, 12550247355579289071], [16230660778393187092, "tracing_subscriber", false, 18268765655978278090], [17392930845109885325, "kameo", false, 3008133844171748266]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hag-hvac-618415a9f3a610c3/dep-test-bin-hag-hvac", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}