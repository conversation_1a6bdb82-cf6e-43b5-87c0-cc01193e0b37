{"rustc": 7501422222776872140, "features": "[\"macros\"]", "declared_features": "[\"bench\", \"datagen\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"experimental\", \"log_error_context\", \"logging\", \"macros\", \"serde\", \"std\", \"sync\"]", "target": *******************, "profile": 5638897767485582800, "path": 17836402260949316459, "deps": [[1218499983858120347, "icu_locid", false, 11055256631272028991], [*******************, "stable_deref_trait", false, 354082982027106783], [5298260564258778412, "displaydoc", false, 16002632564527668653], [6573907571211658978, "zerofrom", false, 17054216855648319839], [8375043423015247637, "tinystr", false, 16564637387399242707], [11734826517668253792, "writeable", false, 11960200444554780988], [14072360194315679348, "zerovec", false, 8587931813771838184], [15837516966853249478, "icu_provider_macros", false, 17349388172813805745], [17505694197072478820, "yoke", false, 2952612319252667032]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/icu_provider-c275e6a5e565f294/dep-lib-icu_provider", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}