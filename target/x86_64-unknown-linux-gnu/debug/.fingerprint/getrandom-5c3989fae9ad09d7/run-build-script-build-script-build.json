{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12546824318118147037, "build_script_build", false, 5337416934052462154]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/debug/build/getrandom-5c3989fae9ad09d7/output", "paths": ["build.rs"]}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}