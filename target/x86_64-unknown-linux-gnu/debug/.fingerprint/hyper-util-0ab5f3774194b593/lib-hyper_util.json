{"rustc": 7501422222776872140, "features": "[\"client\", \"client-legacy\", \"default\", \"http1\", \"tokio\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"client\", \"client-legacy\", \"default\", \"full\", \"http1\", \"http2\", \"server\", \"server-auto\", \"server-graceful\", \"service\", \"tokio\"]", "target": 11100538814903412163, "profile": 12197320342853292988, "path": 8873410972515250375, "deps": [[784494742817713399, "tower_service", false, 8030510458800308454], [828094305214142069, "http", false, 3566128456673887320], [1811549171721445101, "futures_channel", false, 18207397079183377096], [1906322745568073236, "pin_project_lite", false, 897829523511829008], [3264248519883990135, "socket2", false, 10706664426053824622], [7489145127516980061, "bytes", false, 4053534707370644211], [8606274917505247608, "tracing", false, 1775809060611537187], [9538054652646069845, "tokio", false, 5241801424725673318], [10629569228670356391, "futures_util", false, 16545482580899732429], [11957360342995674422, "hyper", false, 2602327333493346112], [14084095096285906100, "http_body", false, 10801992549226321403]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hyper-util-0ab5f3774194b593/dep-lib-hyper_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}