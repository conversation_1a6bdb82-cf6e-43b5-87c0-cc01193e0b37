{"rustc": 7501422222776872140, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 5638897767485582800, "path": 1631200221287801540, "deps": [[9559541369283268958, "libc", false, 18153548133616429524], [10411997081178400487, "cfg_if", false, 3532607937017066775]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/getrandom-312895e4415d69a3/dep-lib-getrandom", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}