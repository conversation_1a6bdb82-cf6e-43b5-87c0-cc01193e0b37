{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[]", "target": 1347177389841305098, "profile": 10795950532079515987, "path": 14672471563019832539, "deps": [[411067296443658118, "serde", false, 16605744961628436596], [1173784911866891520, "config", false, 7947485394257150014], [1791586607557829099, "serde_json", false, 5333526002413214805], [1931345177571435668, "hag_core", false, 2870593518951105280], [2062481783838671931, "parking_lot", false, 17230048051115447529], [6280640092497431962, "options", false, 3548276106249029668], [6612275129670119762, "smlang", false, 3980850214526958397], [7228062143122072479, "kameo_actors", false, 5821740507760426412], [7986280626685324278, "di", false, 10985861477848370000], [8606274917505247608, "tracing", false, 7697175795440132406], [9538054652646069845, "tokio", false, 1878114674913405815], [9614479274285663593, "serde_yaml", false, 3739827583633947784], [10525753287504657187, "time", false, 1205973566453176196], [11026273334807506675, "snafu", false, 6686343208679785834], [11736783209744844508, "hass_rs", false, 3595827241630648990], [13787082801403653804, "async_trait", false, 12550247355579289071], [16230660778393187092, "tracing_subscriber", false, 11905620109240488843], [17392930845109885325, "kameo", false, 14971278914189117293]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hag-hvac-2ff285345a6028ed/dep-test-bin-hag-hvac", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}