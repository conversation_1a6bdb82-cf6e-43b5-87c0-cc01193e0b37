{"rustc": 7501422222776872140, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 10452971554943060093, "path": 9874546048964616035, "deps": [[5103565458935487, "futures_io", false, 16222518839697476767], [1811549171721445101, "futures_channel", false, 18207397079183377096], [7013762810557009322, "futures_sink", false, 34266912003835966], [7620660491849607393, "futures_core", false, 8167891108675841539], [10629569228670356391, "futures_util", false, 16545482580899732429], [12779779637805422465, "futures_executor", false, 12803280914659973090], [16240732885093539806, "futures_task", false, 2082042607970262394]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-3947302b270ab764/dep-lib-futures", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}