{"rustc": 7501422222776872140, "features": "[\"default\", \"std\", \"sync\"]", "declared_features": "[\"default\", \"std\", \"sync\"]", "target": 789766427714428579, "profile": 5638897767485582800, "path": 14916367532627393741, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/downcast-rs-4342190a6f5b94df/dep-lib-downcast_rs", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}