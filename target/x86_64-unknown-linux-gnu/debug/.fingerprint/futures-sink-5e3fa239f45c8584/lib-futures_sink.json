{"rustc": 7501422222776872140, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 10827111567014737887, "profile": 2311051578282775386, "path": 5158464377630399458, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-sink-5e3fa239f45c8584/dep-lib-futures_sink", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}