{"rustc": 7501422222776872140, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"std\"]", "target": 5317002649412810881, "profile": 12197320342853292988, "path": 9564266146009415234, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/stable_deref_trait-29386632eb3f70e5/dep-lib-stable_deref_trait", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}