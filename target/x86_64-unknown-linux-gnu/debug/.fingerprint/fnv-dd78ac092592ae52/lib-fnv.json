{"rustc": 7501422222776872140, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"std\"]", "target": 10248144769085601448, "profile": 5638897767485582800, "path": 11148342032872140440, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/fnv-dd78ac092592ae52/dep-lib-fnv", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}