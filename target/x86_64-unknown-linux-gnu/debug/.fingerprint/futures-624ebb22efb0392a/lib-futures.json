{"rustc": 7501422222776872140, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 2311051578282775386, "path": 9874546048964616035, "deps": [[5103565458935487, "futures_io", false, 7053986525484373085], [1811549171721445101, "futures_channel", false, 5457328690396976161], [7013762810557009322, "futures_sink", false, 17814784244059756285], [7620660491849607393, "futures_core", false, 10528979038097258143], [10629569228670356391, "futures_util", false, 17460236509704004415], [12779779637805422465, "futures_executor", false, 13354686854669040670], [16240732885093539806, "futures_task", false, 11264908290563341571]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-624ebb22efb0392a/dep-lib-futures", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}