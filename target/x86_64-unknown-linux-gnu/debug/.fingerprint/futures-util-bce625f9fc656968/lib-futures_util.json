{"rustc": 7501422222776872140, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 2311051578282775386, "path": 11948470764360580879, "deps": [[5103565458935487, "futures_io", false, 7053986525484373085], [1615478164327904835, "pin_utils", false, 16838390267599928315], [1811549171721445101, "futures_channel", false, 5457328690396976161], [1906322745568073236, "pin_project_lite", false, 15490304558171860351], [3129130049864710036, "memchr", false, 3278891040995196140], [6955678925937229351, "slab", false, 15249217112054956231], [7013762810557009322, "futures_sink", false, 17814784244059756285], [7620660491849607393, "futures_core", false, 10528979038097258143], [10565019901765856648, "futures_macro", false, 10757395850490437281], [16240732885093539806, "futures_task", false, 11264908290563341571]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-util-bce625f9fc656968/dep-lib-futures_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}