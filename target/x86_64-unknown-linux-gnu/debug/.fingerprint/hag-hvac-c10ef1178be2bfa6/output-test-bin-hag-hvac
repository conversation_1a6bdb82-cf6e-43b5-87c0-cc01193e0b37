{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":978,"byte_end":1009,"line_start":37,"line_end":37,"column_start":17,"column_end":48,"is_primary":true,"text":[{"text":"    supervisor: ActorRef<HassServiceSupervisor>,","highlight_start":17,"highlight_end":48}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:37:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    supervisor: ActorRef<HassServiceSupervisor>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `start` has an incompatible type for trait","code":{"code":"E0053","explanation":"The parameters of any trait method must match between a trait implementation\nand the trait definition.\n\nErroneous code example:\n\n```compile_fail,E0053\ntrait Foo {\n    fn foo(x: u16);\n    fn bar(&self);\n}\n\nstruct Bar;\n\nimpl Foo for Bar {\n    // error, expected u16, found i16\n    fn foo(x: i16) { }\n\n    // error, types differ in mutability\n    fn bar(&mut self) { }\n}\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":1284,"byte_end":1315,"line_start":51,"line_end":51,"column_start":21,"column_end":52,"is_primary":true,"text":[{"text":"        supervisor: ActorRef<HassServiceSupervisor>,","highlight_start":21,"highlight_end":52}],"label":"expected `ActorRef<HassServiceSupervisor>`, found a different `ActorRef<HassServiceSupervisor>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected signature `fn(&'life0 HvacHandler, kameo::actor::actor_ref::ActorRef<HassServiceSupervisor>, kameo::actor::actor_ref::ActorRef<kameo_actors::pubsub::PubSub<HassEvent>>, HassContext) -> Pin<_>`\n   found signature `fn(&'life0 HvacHandler, ActorRef<HassServiceSupervisor>, ActorRef<PubSub<HassEvent>>, HassContext) -> Pin<_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"change the parameter type to match the trait","code":null,"level":"help","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":1284,"byte_end":1315,"line_start":51,"line_end":51,"column_start":21,"column_end":52,"is_primary":true,"text":[{"text":"        supervisor: ActorRef<HassServiceSupervisor>,","highlight_start":21,"highlight_end":52}],"label":null,"suggested_replacement":"kameo::actor::actor_ref::ActorRef<HassServiceSupervisor>","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0053]\u001b[0m\u001b[0m\u001b[1m: method `start` has an incompatible type for trait\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:51:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        supervisor: ActorRef<HassServiceSupervisor>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `ActorRef<HassServiceSupervisor>`, found a different `ActorRef<HassServiceSupervisor>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected signature `fn(&'life0 HvacHandler, \u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo::actor::actor_ref::ActorRef<HassServiceSupervisor>\u001b[0m\u001b[0m, \u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo::actor::actor_ref::ActorRef<kameo_actors::pubsub::PubSub<HassEvent>>\u001b[0m\u001b[0m, HassContext) -> Pin<_>`\u001b[0m\n\u001b[0m              found signature `fn(&'life0 HvacHandler, \u001b[0m\u001b[0m\u001b[1m\u001b[35mActorRef<HassServiceSupervisor>\u001b[0m\u001b[0m, \u001b[0m\u001b[0m\u001b[1m\u001b[35mActorRef<PubSub<HassEvent>>\u001b[0m\u001b[0m, HassContext) -> Pin<_>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change the parameter type to match the trait\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        supervisor: \u001b[0m\u001b[0m\u001b[38;5;10mkameo::actor::actor_ref::\u001b[0m\u001b[0mActorRef<HassServiceSupervisor>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":7877,"byte_end":7895,"line_start":221,"line_end":221,"column_start":17,"column_end":35,"is_primary":true,"text":[{"text":"    state_mgmt: ActorRef<HassMgmt>,","highlight_start":17,"highlight_end":35}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:221:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state_mgmt: ActorRef<HassMgmt>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":3019,"byte_end":4312,"line_start":108,"line_end":125,"column_start":1,"column_end":2,"is_primary":true,"text":[{"text":"statemachine! {","highlight_start":1,"highlight_end":16},{"text":"    derive_states: [Debug, Clone],","highlight_start":1,"highlight_end":35},{"text":"    custom_error: true,","highlight_start":1,"highlight_end":24},{"text":"    transitions: {","highlight_start":1,"highlight_end":19},{"text":"        // *Init + StateChanged(StateChangeData) / async switch_off = Off,","highlight_start":1,"highlight_end":75},{"text":"        *Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":136},{"text":"        Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":118},{"text":"        Off + StateChanged(StateChangeData) [!can_operate || is_temp_too_high || (!is_temp_too_low && !is_temp_too_high)] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":156},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":120},{"text":"        Heating + StateChanged(StateChangeData) [!can_operate || is_temp_too_high] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":117},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && (is_temp_too_low || !is_temp_too_high)] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":145},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Defrosting + StateChanged(StateChangeData) [is_defrost_cycle_completed] / async stop_defrost = Off,","highlight_start":1,"highlight_end":108},{"text":"        Defrosting + StateChanged(StateChangeData) [!is_defrost_cycle_completed] / continue_defrost = Defrosting,","highlight_start":1,"highlight_end":114},{"text":"        Defrosting + StateChanged(StateChangeData) [!can_operate]  / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":101},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":3019,"byte_end":4312,"line_start":108,"line_end":125,"column_start":1,"column_end":2,"is_primary":false,"text":[{"text":"statemachine! {","highlight_start":1,"highlight_end":16},{"text":"    derive_states: [Debug, Clone],","highlight_start":1,"highlight_end":35},{"text":"    custom_error: true,","highlight_start":1,"highlight_end":24},{"text":"    transitions: {","highlight_start":1,"highlight_end":19},{"text":"        // *Init + StateChanged(StateChangeData) / async switch_off = Off,","highlight_start":1,"highlight_end":75},{"text":"        *Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":136},{"text":"        Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":118},{"text":"        Off + StateChanged(StateChangeData) [!can_operate || is_temp_too_high || (!is_temp_too_low && !is_temp_too_high)] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":156},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":120},{"text":"        Heating + StateChanged(StateChangeData) [!can_operate || is_temp_too_high] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":117},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && (is_temp_too_low || !is_temp_too_high)] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":145},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Defrosting + StateChanged(StateChangeData) [is_defrost_cycle_completed] / async stop_defrost = Off,","highlight_start":1,"highlight_end":108},{"text":"        Defrosting + StateChanged(StateChangeData) [!is_defrost_cycle_completed] / continue_defrost = Defrosting,","highlight_start":1,"highlight_end":114},{"text":"        Defrosting + StateChanged(StateChangeData) [!can_operate]  / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":101},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"statemachine!","def_site_span":{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/smlang-macros-0.8.0/src/lib.rs","byte_start":250,"byte_end":328,"line_start":16,"line_end":16,"column_start":1,"column_end":79,"is_primary":false,"text":[{"text":"pub fn statemachine(input: proc_macro::TokenStream) -> proc_macro::TokenStream {","highlight_start":1,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:108:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mstatemachine! {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    derive_states: [Debug, Clone],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    custom_error: true,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    transitions: {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `statemachine` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":3019,"byte_end":4312,"line_start":108,"line_end":125,"column_start":1,"column_end":2,"is_primary":true,"text":[{"text":"statemachine! {","highlight_start":1,"highlight_end":16},{"text":"    derive_states: [Debug, Clone],","highlight_start":1,"highlight_end":35},{"text":"    custom_error: true,","highlight_start":1,"highlight_end":24},{"text":"    transitions: {","highlight_start":1,"highlight_end":19},{"text":"        // *Init + StateChanged(StateChangeData) / async switch_off = Off,","highlight_start":1,"highlight_end":75},{"text":"        *Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":136},{"text":"        Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":118},{"text":"        Off + StateChanged(StateChangeData) [!can_operate || is_temp_too_high || (!is_temp_too_low && !is_temp_too_high)] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":156},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":120},{"text":"        Heating + StateChanged(StateChangeData) [!can_operate || is_temp_too_high] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":117},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && (is_temp_too_low || !is_temp_too_high)] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":145},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Defrosting + StateChanged(StateChangeData) [is_defrost_cycle_completed] / async stop_defrost = Off,","highlight_start":1,"highlight_end":108},{"text":"        Defrosting + StateChanged(StateChangeData) [!is_defrost_cycle_completed] / continue_defrost = Defrosting,","highlight_start":1,"highlight_end":114},{"text":"        Defrosting + StateChanged(StateChangeData) [!can_operate]  / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":101},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":3019,"byte_end":4312,"line_start":108,"line_end":125,"column_start":1,"column_end":2,"is_primary":false,"text":[{"text":"statemachine! {","highlight_start":1,"highlight_end":16},{"text":"    derive_states: [Debug, Clone],","highlight_start":1,"highlight_end":35},{"text":"    custom_error: true,","highlight_start":1,"highlight_end":24},{"text":"    transitions: {","highlight_start":1,"highlight_end":19},{"text":"        // *Init + StateChanged(StateChangeData) / async switch_off = Off,","highlight_start":1,"highlight_end":75},{"text":"        *Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":136},{"text":"        Off + StateChanged(StateChangeData) [can_operate && is_temp_too_low] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":118},{"text":"        Off + StateChanged(StateChangeData) [!can_operate || is_temp_too_high || (!is_temp_too_low && !is_temp_too_high)] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":156},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && need_defrost_cycle] / async start_defrost = Defrosting,","highlight_start":1,"highlight_end":120},{"text":"        Heating + StateChanged(StateChangeData) [!can_operate || is_temp_too_high] / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":117},{"text":"        Heating + StateChanged(StateChangeData) [can_operate && (is_temp_too_low || !is_temp_too_high)] / async start_or_stay_heating = Heating,","highlight_start":1,"highlight_end":145},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Defrosting + StateChanged(StateChangeData) [is_defrost_cycle_completed] / async stop_defrost = Off,","highlight_start":1,"highlight_end":108},{"text":"        Defrosting + StateChanged(StateChangeData) [!is_defrost_cycle_completed] / continue_defrost = Defrosting,","highlight_start":1,"highlight_end":114},{"text":"        Defrosting + StateChanged(StateChangeData) [!can_operate]  / async switch_or_stay_off = Off,","highlight_start":1,"highlight_end":101},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `async` block or function","def_site_span":{"file_name":"hag-hvac/src/main.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:108:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0mstatemachine! {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    derive_states: [Debug, Clone],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    custom_error: true,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    transitions: {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":5190,"byte_end":5218,"line_start":159,"line_end":160,"column_start":35,"column_end":18,"is_primary":false,"text":[{"text":"            let eval_attr_value = hass_actor","highlight_start":35,"highlight_end":45},{"text":"                .ask(GetAttribute {","highlight_start":1,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":5218,"byte_end":5221,"line_start":160,"line_end":160,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"                .ask(GetAttribute {","highlight_start":18,"highlight_end":21}],"label":"method cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `HassMgmt: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassMgmt: Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:160:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let eval_attr_value = hass_actor\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m ___________________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .ask(GetAttribute {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassMgmt: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassMgmt: Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":5964,"byte_end":6000,"line_start":177,"line_end":178,"column_start":21,"column_end":26,"is_primary":false,"text":[{"text":"                    hass_actor","highlight_start":21,"highlight_end":31},{"text":"                        .ask(SetStateOrAttribute {","highlight_start":1,"highlight_end":26}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":6000,"byte_end":6003,"line_start":178,"line_end":178,"column_start":26,"column_end":29,"is_primary":true,"text":[{"text":"                        .ask(SetStateOrAttribute {","highlight_start":26,"highlight_end":29}],"label":"method cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `HassMgmt: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassMgmt: Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:178:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    hass_actor\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .ask(SetStateOrAttribute {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassMgmt: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassMgmt: Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":6560,"byte_end":6596,"line_start":189,"line_end":190,"column_start":21,"column_end":26,"is_primary":false,"text":[{"text":"                    hass_actor","highlight_start":21,"highlight_end":31},{"text":"                        .ask(SetStateOrAttribute {","highlight_start":1,"highlight_end":26}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":6596,"byte_end":6599,"line_start":190,"line_end":190,"column_start":26,"column_end":29,"is_primary":true,"text":[{"text":"                        .ask(SetStateOrAttribute {","highlight_start":26,"highlight_end":29}],"label":"method cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `HassMgmt: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassMgmt: Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:190:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    hass_actor\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .ask(SetStateOrAttribute {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassMgmt: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassMgmt: Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":7156,"byte_end":7192,"line_start":201,"line_end":202,"column_start":21,"column_end":26,"is_primary":false,"text":[{"text":"                    hass_actor","highlight_start":21,"highlight_end":31},{"text":"                        .ask(SetStateOrAttribute {","highlight_start":1,"highlight_end":26}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":7192,"byte_end":7195,"line_start":202,"line_end":202,"column_start":26,"column_end":29,"is_primary":true,"text":[{"text":"                        .ask(SetStateOrAttribute {","highlight_start":26,"highlight_end":29}],"label":"method cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `HassMgmt: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassMgmt: Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:202:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    hass_actor\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m202\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        .ask(SetStateOrAttribute {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassMgmt: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassMgmt: Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":9325,"byte_end":9350,"line_start":261,"line_end":261,"column_start":14,"column_end":39,"is_primary":true,"text":[{"text":"        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |current_temp, idx| {","highlight_start":14,"highlight_end":39}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:261:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m261\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |current_temp, idx| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `clone` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":9367,"byte_end":9372,"line_start":261,"line_end":261,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |current_temp, idx| {","highlight_start":56,"highlight_end":61}],"label":"method cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1349,"byte_end":1378,"line_start":44,"line_end":44,"column_start":1,"column_end":30,"is_primary":false,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":1,"highlight_end":30}],"label":"doesn't satisfy `ActorRef<HassMgmt>: Clone`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `HassMgmt: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassMgmt: Actor`\nwhich is required by `ActorRef<HassMgmt>: Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `clone` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:261:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m261\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |current_temp, idx| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `ActorRef<HassMgmt>: Clone`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassMgmt: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassMgmt: Actor`\u001b[0m\n\u001b[0m            which is required by `ActorRef<HassMgmt>: Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":10047,"byte_end":10072,"line_start":282,"line_end":282,"column_start":14,"column_end":39,"is_primary":true,"text":[{"text":"        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |_, _idx| {","highlight_start":14,"highlight_end":39}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:282:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |_, _idx| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `clone` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":10089,"byte_end":10094,"line_start":282,"line_end":282,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |_, _idx| {","highlight_start":56,"highlight_end":61}],"label":"method cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1349,"byte_end":1378,"line_start":44,"line_end":44,"column_start":1,"column_end":30,"is_primary":false,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":1,"highlight_end":30}],"label":"doesn't satisfy `ActorRef<HassMgmt>: Clone`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `HassMgmt: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassMgmt: Actor`\nwhich is required by `ActorRef<HassMgmt>: Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `clone` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:282:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.set_units_mode_and_preset(data.state_mgmt.clone(), \"current_temperature\", |_, _idx| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `ActorRef<HassMgmt>: Clone`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassMgmt: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassMgmt: Actor`\u001b[0m\n\u001b[0m            which is required by `ActorRef<HassMgmt>: Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":11689,"byte_end":11722,"line_start":321,"line_end":322,"column_start":13,"column_end":18,"is_primary":false,"text":[{"text":"            data.state_mgmt","highlight_start":13,"highlight_end":28},{"text":"                .ask(SetStateOrAttribute {","highlight_start":1,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":11722,"byte_end":11725,"line_start":322,"line_end":322,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"                .ask(SetStateOrAttribute {","highlight_start":18,"highlight_end":21}],"label":"method cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"doesn't satisfy `HassMgmt: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassMgmt: Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `ask` exists for struct `ActorRef<HassMgmt>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:322:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m321\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            data.state_mgmt\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .ask(SetStateOrAttribute {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassMgmt>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassMgmt: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassMgmt: Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":14147,"byte_end":14151,"line_start":390,"line_end":390,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    async fn stop_defrost(&mut self, data: StateChangeData) -> WhateverResult<()> {","highlight_start":38,"highlight_end":42}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:390:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m390\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn stop_defrost(&mut self, data: StateChangeData) -> WhateverResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":14147,"byte_end":14151,"line_start":390,"line_end":390,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    async fn stop_defrost(&mut self, data: StateChangeData) -> WhateverResult<()> {","highlight_start":38,"highlight_end":42}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":14147,"byte_end":14151,"line_start":390,"line_end":390,"column_start":38,"column_end":42,"is_primary":false,"text":[{"text":"    async fn stop_defrost(&mut self, data: StateChangeData) -> WhateverResult<()> {","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `async` block or function","def_site_span":{"file_name":"hag-hvac/src/main.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:390:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m390\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn stop_defrost(&mut self, data: StateChangeData) -> WhateverResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `ask` exists for struct `ActorRef<HassServiceSupervisor>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15995,"byte_end":16054,"line_start":444,"line_end":447,"column_start":25,"column_end":14,"is_primary":false,"text":[{"text":"        let hass_mgmt = self","highlight_start":25,"highlight_end":29},{"text":"            .ctx","highlight_start":1,"highlight_end":17},{"text":"            .supervisor","highlight_start":1,"highlight_end":24},{"text":"            .ask(GetHassMgmt)","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":16054,"byte_end":16057,"line_start":447,"line_end":447,"column_start":14,"column_end":17,"is_primary":true,"text":[{"text":"            .ask(GetHassMgmt)","highlight_start":14,"highlight_end":17}],"label":"method cannot be called on `ActorRef<HassServiceSupervisor>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"doesn't satisfy `HassServiceSupervisor: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassServiceSupervisor: Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `ask` exists for struct `ActorRef<HassServiceSupervisor>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:447:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m444\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let hass_mgmt = self\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m _________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m445\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .ctx\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m446\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .supervisor\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m447\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .ask(GetHassMgmt)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassServiceSupervisor>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassServiceSupervisor: Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `HassServiceSupervisor: Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":16896,"byte_end":16905,"line_start":472,"line_end":472,"column_start":25,"column_end":34,"is_primary":true,"text":[{"text":"            state_mgmt: hass_mgmt,","highlight_start":25,"highlight_end":34}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:472:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m472\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            state_mgmt: hass_mgmt,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":17618,"byte_end":17751,"line_start":497,"line_end":502,"column_start":60,"column_end":6,"is_primary":true,"text":[{"text":"    async fn new(ctx: HvacContext) -> WhateverResult<Self> {","highlight_start":60,"highlight_end":61},{"text":"        Ok(Self {","highlight_start":1,"highlight_end":18},{"text":"            ctx: ctx.clone(),","highlight_start":1,"highlight_end":30},{"text":"            state_machine: StateMachine::new(SMContext::new(ctx)),","highlight_start":1,"highlight_end":67},{"text":"        })","highlight_start":1,"highlight_end":11},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:497:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m497\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    async fn new(ctx: HvacContext) -> WhateverResult<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ____________________________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m498\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Ok(Self {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m499\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ctx: ctx.clone(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m500\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            state_machine: StateMachine::new(SMContext::new(ctx)),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m501\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        })\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m502\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":17576,"byte_end":17579,"line_start":497,"line_end":497,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    async fn new(ctx: HvacContext) -> WhateverResult<Self> {","highlight_start":18,"highlight_end":21}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:497:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m497\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn new(ctx: HvacContext) -> WhateverResult<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":17631,"byte_end":17744,"line_start":498,"line_end":501,"column_start":12,"column_end":10,"is_primary":true,"text":[{"text":"        Ok(Self {","highlight_start":12,"highlight_end":18},{"text":"            ctx: ctx.clone(),","highlight_start":1,"highlight_end":30},{"text":"            state_machine: StateMachine::new(SMContext::new(ctx)),","highlight_start":1,"highlight_end":67},{"text":"        })","highlight_start":1,"highlight_end":10}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:498:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m498\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        Ok(Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ____________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m499\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ctx: ctx.clone(),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m500\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            state_machine: StateMachine::new(SMContext::new(ctx)),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m501\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        })\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":17695,"byte_end":17733,"line_start":500,"line_end":500,"column_start":28,"column_end":66,"is_primary":true,"text":[{"text":"            state_machine: StateMachine::new(SMContext::new(ctx)),","highlight_start":28,"highlight_end":66}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:500:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m500\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            state_machine: StateMachine::new(SMContext::new(ctx)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":17713,"byte_end":17732,"line_start":500,"line_end":500,"column_start":46,"column_end":65,"is_primary":true,"text":[{"text":"            state_machine: StateMachine::new(SMContext::new(ctx)),","highlight_start":46,"highlight_end":65}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:500:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m500\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            state_machine: StateMachine::new(SMContext::new(ctx)),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":17576,"byte_end":17579,"line_start":497,"line_end":497,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    async fn new(ctx: HvacContext) -> WhateverResult<Self> {","highlight_start":18,"highlight_end":21}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":17576,"byte_end":17579,"line_start":497,"line_end":497,"column_start":18,"column_end":21,"is_primary":false,"text":[{"text":"    async fn new(ctx: HvacContext) -> WhateverResult<Self> {","highlight_start":18,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `async` block or function","def_site_span":{"file_name":"hag-hvac/src/main.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:497:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m497\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn new(ctx: HvacContext) -> WhateverResult<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied in `HvacActor`","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15348,"byte_end":15357,"line_start":426,"line_end":426,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"pub struct HvacActor {","highlight_start":12,"highlight_end":21}],"label":"within `HvacActor`, the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required because it appears within the type `ActorRef<HassServiceSupervisor>`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1360,"byte_end":1368,"line_start":44,"line_end":44,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required because it appears within the type `HvacContext`","code":null,"level":"note","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":887,"byte_end":898,"line_start":34,"line_end":34,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"pub struct HvacContext {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required because it appears within the type `HvacActor`","code":null,"level":"note","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15348,"byte_end":15357,"line_start":426,"line_end":426,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"pub struct HvacActor {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required by a bound in `Actor`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4148,"byte_end":4152,"line_start":110,"line_end":110,"column_start":26,"column_end":30,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":26,"highlight_end":30}],"label":"required by this bound in `Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied in `HvacActor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:426:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m426\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HvacActor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mwithin `HvacActor`, the trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `ActorRef<HassServiceSupervisor>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `HvacContext`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:34:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HvacContext {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `HvacActor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:426:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m426\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HvacActor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `Actor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied in `HvacActor`","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15329,"byte_end":15334,"line_start":425,"line_end":425,"column_start":10,"column_end":15,"is_primary":true,"text":[{"text":"#[derive(Actor)]","highlight_start":10,"highlight_end":15}],"label":"within `HvacActor`, the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15329,"byte_end":15334,"line_start":425,"line_end":425,"column_start":10,"column_end":15,"is_primary":false,"text":[{"text":"#[derive(Actor)]","highlight_start":10,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Actor)]","def_site_span":{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo_macros-0.14.0/src/lib.rs","byte_start":3024,"byte_end":3078,"line_start":106,"line_end":106,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn derive_actor(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required because it appears within the type `ActorRef<HassServiceSupervisor>`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1360,"byte_end":1368,"line_start":44,"line_end":44,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required because it appears within the type `HvacContext`","code":null,"level":"note","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":887,"byte_end":898,"line_start":34,"line_end":34,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"pub struct HvacContext {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required because it appears within the type `HvacActor`","code":null,"level":"note","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15348,"byte_end":15357,"line_start":426,"line_end":426,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"pub struct HvacActor {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required by a bound in `Actor`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4148,"byte_end":4152,"line_start":110,"line_end":110,"column_start":26,"column_end":30,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":26,"highlight_end":30}],"label":"required by this bound in `Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied in `HvacActor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:425:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m425\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Actor)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mwithin `HvacActor`, the trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `ActorRef<HassServiceSupervisor>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `HvacContext`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:34:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HvacContext {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `HvacActor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:426:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m426\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HvacActor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `Actor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Actor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Actor` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied in `HvacActor`","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15468,"byte_end":15477,"line_start":431,"line_end":431,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"impl Message<HassEvent> for HvacActor {","highlight_start":29,"highlight_end":38}],"label":"within `HvacActor`, the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required because it appears within the type `ActorRef<HassServiceSupervisor>`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1360,"byte_end":1368,"line_start":44,"line_end":44,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required because it appears within the type `HvacContext`","code":null,"level":"note","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":887,"byte_end":898,"line_start":34,"line_end":34,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"pub struct HvacContext {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required because it appears within the type `HvacActor`","code":null,"level":"note","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":15348,"byte_end":15357,"line_start":426,"line_end":426,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"pub struct HvacActor {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required for `HvacActor` to implement `Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `kameo::message::Message`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/message.rs","byte_start":1939,"byte_end":1944,"line_start":37,"line_end":37,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"pub trait Message<T: Send + 'static>: Actor {","highlight_start":39,"highlight_end":44}],"label":"required by this bound in `Message`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied in `HvacActor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:431:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m431\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Message<HassEvent> for HvacActor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mwithin `HvacActor`, the trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `ActorRef<HassServiceSupervisor>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `HvacContext`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:34:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HvacContext {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required because it appears within the type `HvacActor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:426:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m426\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HvacActor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `HvacActor` to implement `Actor`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `kameo::message::Message`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/message.rs:37:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Message<T: Send + 'static>: Actor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Message`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":1284,"byte_end":1315,"line_start":51,"line_end":51,"column_start":21,"column_end":52,"is_primary":true,"text":[{"text":"        supervisor: ActorRef<HassServiceSupervisor>,","highlight_start":21,"highlight_end":52}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:51:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        supervisor: ActorRef<HassServiceSupervisor>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":4729,"byte_end":4747,"line_start":144,"line_end":144,"column_start":21,"column_end":39,"is_primary":true,"text":[{"text":"        hass_actor: ActorRef<HassMgmt>,","highlight_start":21,"highlight_end":39}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:144:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        hass_actor: ActorRef<HassMgmt>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":367,"byte_end":398,"line_start":14,"line_end":14,"column_start":21,"column_end":52,"is_primary":true,"text":[{"text":"        supervisor: ActorRef<HassServiceSupervisor>,","highlight_start":21,"highlight_end":52}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:14:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        supervisor: ActorRef<HassServiceSupervisor>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":966,"byte_end":1009,"line_start":37,"line_end":37,"column_start":5,"column_end":48,"is_primary":true,"text":[{"text":"    supervisor: ActorRef<HassServiceSupervisor>,","highlight_start":5,"highlight_end":48}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":868,"byte_end":873,"line_start":33,"line_end":33,"column_start":10,"column_end":15,"is_primary":false,"text":[{"text":"#[derive(Clone)]","highlight_start":10,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Clone)]","def_site_span":{"file_name":"/home/<USER>/.rustup/toolchains/nightly-x86_64-unknown-linux-gnu/lib/rustlib/src/rust/library/core/src/clone.rs","byte_start":7504,"byte_end":7519,"line_start":220,"line_end":220,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"pub macro Clone($item:item) {","highlight_start":1,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:37:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Clone)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12min this derive macro expansion\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    supervisor: ActorRef<HassServiceSupervisor>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `clone` exists for struct `ActorRef<HassServiceSupervisor>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":1611,"byte_end":1616,"line_start":58,"line_end":58,"column_start":36,"column_end":41,"is_primary":true,"text":[{"text":"            supervisor: supervisor.clone(),","highlight_start":36,"highlight_end":41}],"label":"method cannot be called on `ActorRef<HassServiceSupervisor>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"doesn't satisfy `HassServiceSupervisor: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1349,"byte_end":1378,"line_start":44,"line_end":44,"column_start":1,"column_end":30,"is_primary":false,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":1,"highlight_end":30}],"label":"doesn't satisfy `ActorRef<HassServiceSupervisor>: Clone`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassServiceSupervisor: Actor`\nwhich is required by `ActorRef<HassServiceSupervisor>: Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `clone` exists for struct `ActorRef<HassServiceSupervisor>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:58:36\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            supervisor: supervisor.clone(),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassServiceSupervisor>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassServiceSupervisor: Actor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `ActorRef<HassServiceSupervisor>: Clone`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m           `HassServiceSupervisor: Actor`\u001b[0m\n\u001b[0m           which is required by `ActorRef<HassServiceSupervisor>: Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":1600,"byte_end":1618,"line_start":58,"line_end":58,"column_start":25,"column_end":43,"is_primary":true,"text":[{"text":"            supervisor: supervisor.clone(),","highlight_start":25,"highlight_end":43}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:58:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m58\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            supervisor: supervisor.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `link` exists for struct `ActorRef<HassServiceSupervisor>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":1723,"byte_end":1727,"line_start":63,"line_end":63,"column_start":20,"column_end":24,"is_primary":true,"text":[{"text":"        supervisor.link(&actor_ref).await;","highlight_start":20,"highlight_end":24}],"label":"method cannot be called on `ActorRef<HassServiceSupervisor>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"doesn't satisfy `HassServiceSupervisor: Actor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`HassServiceSupervisor: Actor`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `link` exists for struct `ActorRef<HassServiceSupervisor>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:63:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        supervisor.link(&actor_ref).await;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `ActorRef<HassServiceSupervisor>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `HassServiceSupervisor: Actor`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m           `HassServiceSupervisor: Actor`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":7865,"byte_end":7895,"line_start":221,"line_end":221,"column_start":5,"column_end":35,"is_primary":true,"text":[{"text":"    state_mgmt: ActorRef<HassMgmt>,","highlight_start":5,"highlight_end":35}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":7817,"byte_end":7822,"line_start":219,"line_end":219,"column_start":10,"column_end":15,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone)]","highlight_start":10,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Debug)]","def_site_span":{"file_name":"/home/<USER>/.rustup/toolchains/nightly-x86_64-unknown-linux-gnu/lib/rustlib/src/rust/library/core/src/fmt/mod.rs","byte_start":34096,"byte_end":34111,"line_start":907,"line_end":907,"column_start":5,"column_end":20,"is_primary":false,"text":[{"text":"    pub macro Debug($item:item) {","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `ActorRef<HassMgmt>` to implement `Debug`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for the cast from `&ActorRef<HassMgmt>` to `&dyn Debug`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:221:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m219\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12min this derive macro expansion\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m220\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct StateChangeData {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state_mgmt: ActorRef<HassMgmt>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `ActorRef<HassMgmt>` to implement `Debug`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for the cast from `&ActorRef<HassMgmt>` to `&dyn Debug`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":7865,"byte_end":7895,"line_start":221,"line_end":221,"column_start":5,"column_end":35,"is_primary":true,"text":[{"text":"    state_mgmt: ActorRef<HassMgmt>,","highlight_start":5,"highlight_end":35}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":7824,"byte_end":7829,"line_start":219,"line_end":219,"column_start":17,"column_end":22,"is_primary":false,"text":[{"text":"#[derive(Debug, Clone)]","highlight_start":17,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Clone)]","def_site_span":{"file_name":"/home/<USER>/.rustup/toolchains/nightly-x86_64-unknown-linux-gnu/lib/rustlib/src/rust/library/core/src/clone.rs","byte_start":7504,"byte_end":7519,"line_start":220,"line_end":220,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"pub macro Clone($item:item) {","highlight_start":1,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:221:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m219\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Debug, Clone)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12min this derive macro expansion\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m220\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct StateChangeData {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state_mgmt: ActorRef<HassMgmt>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/tests/main_test.rs","byte_start":2614,"byte_end":2632,"line_start":78,"line_end":78,"column_start":25,"column_end":43,"is_primary":true,"text":[{"text":"        let state_mgmt: ActorRef<HassMgmt> = hass_service","highlight_start":25,"highlight_end":43}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `ActorRef`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1372,"byte_end":1377,"line_start":44,"line_end":44,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":24,"highlight_end":29}],"label":"required by this bound in `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/tests/main_test.rs:78:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let state_mgmt: ActorRef<HassMgmt> = hass_service\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ActorRef`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`?` operator has incompatible types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/tests/main_test.rs","byte_start":2635,"byte_end":2800,"line_start":78,"line_end":82,"column_start":46,"column_end":78,"is_primary":true,"text":[{"text":"        let state_mgmt: ActorRef<HassMgmt> = hass_service","highlight_start":46,"highlight_end":58},{"text":"            .supervisor()","highlight_start":1,"highlight_end":26},{"text":"            .ask(GetHassMgmt)","highlight_start":1,"highlight_end":30},{"text":"            .await","highlight_start":1,"highlight_end":19},{"text":"            .whatever_context(\"Failed to get state checker from supervisor\")?;","highlight_start":1,"highlight_end":78}],"label":"expected `ActorRef<HassMgmt>`, found a different `ActorRef<HassMgmt>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`?` operator cannot convert from `kameo::actor::actor_ref::ActorRef<HassMgmt>` to `ActorRef<HassMgmt>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"two different versions of crate `kameo` are being used; two types coming from two different versions of the same crate are different types even if they look the same","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs","byte_start":1349,"byte_end":1378,"line_start":44,"line_end":44,"column_start":1,"column_end":30,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":1,"highlight_end":30}],"label":"this is the expected type `ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/actor_ref.rs","byte_start":1281,"byte_end":1310,"line_start":46,"line_end":46,"column_start":1,"column_end":30,"is_primary":true,"text":[{"text":"pub struct ActorRef<A: Actor> {","highlight_start":1,"highlight_end":30}],"label":"this is the found type `kameo::actor::actor_ref::ActorRef`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"return type inferred to be `ActorRef<HassMgmt>` here","code":null,"level":"note","spans":[{"file_name":"hag-hvac/src/tests/main_test.rs","byte_start":1502,"byte_end":1518,"line_start":49,"line_end":49,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"        setup_logging()?;","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"hag-hvac/src/tests/main_test.rs","byte_start":1502,"byte_end":1518,"line_start":49,"line_end":49,"column_start":9,"column_end":25,"is_primary":false,"text":[{"text":"        setup_logging()?;","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of operator `?`","def_site_span":{"file_name":"hag-hvac/src/main.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: `?` operator has incompatible types\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/tests/main_test.rs:78:46\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let state_mgmt: ActorRef<HassMgmt> = hass_service\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .supervisor()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .ask(GetHassMgmt)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .await\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .whatever_context(\"Failed to get state checker from supervisor\")?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________________________________________________________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `ActorRef<HassMgmt>`, found a different `ActorRef<HassMgmt>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `?` operator cannot convert from `kameo::actor::actor_ref::ActorRef<HassMgmt>` to `ActorRef<HassMgmt>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: two different versions of crate `kameo` are being used; two types coming from two different versions of the same crate are different types even if they look the same\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor/actor_ref.rs:44:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the expected type `ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/actor_ref.rs:46:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ActorRef<A: Actor> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the found type `kameo::actor::actor_ref::ActorRef`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `cargo tree` to explore your dependency tree\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: return type inferred to be `ActorRef<HassMgmt>` here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/tests/main_test.rs:49:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        setup_logging()?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":4502,"byte_end":4506,"line_start":134,"line_end":134,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"    fn new(ctx: HvacContext) -> Self {","highlight_start":33,"highlight_end":37}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:134:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn new(ctx: HvacContext) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassServiceSupervisor: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":4481,"byte_end":4484,"line_start":134,"line_end":134,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    fn new(ctx: HvacContext) -> Self {","highlight_start":12,"highlight_end":15}],"label":"the trait `Actor` is not implemented for `HassServiceSupervisor`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs","byte_start":363,"byte_end":395,"line_start":14,"line_end":14,"column_start":1,"column_end":33,"is_primary":false,"text":[{"text":"pub struct HassServiceSupervisor {","highlight_start":1,"highlight_end":33}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassServiceSupervisor: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:134:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn new(ctx: HvacContext) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassServiceSupervisor`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_supervisor.rs:14:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassServiceSupervisor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `HassMgmt: Actor` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"hag-hvac/src/agents/hvac_agent.rs","byte_start":13422,"byte_end":13427,"line_start":370,"line_end":370,"column_start":36,"column_end":41,"is_primary":true,"text":[{"text":"    fn continue_defrost(&mut self, _data: StateChangeData) -> WhateverResult<()> {","highlight_start":36,"highlight_end":41}],"label":"the trait `Actor` is not implemented for `HassMgmt`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there are multiple different versions of crate `kameo` in the dependency graph\n","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs","byte_start":4123,"byte_end":4162,"line_start":110,"line_end":110,"column_start":1,"column_end":40,"is_primary":true,"text":[{"text":"pub trait Actor: Sized + Send + 'static {","highlight_start":1,"highlight_end":40}],"label":"this is the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/agents/mod.rs","byte_start":147,"byte_end":152,"line_start":4,"line_end":4,"column_start":5,"column_end":10,"is_primary":false,"text":[{"text":"use kameo::actor::{pubsub::PubSub, ActorRef};","highlight_start":5,"highlight_end":10}],"label":"one version of crate `kameo` used here, as a direct dependency of the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"hag-hvac/src/main.rs","byte_start":56,"byte_end":64,"line_start":3,"line_end":3,"column_start":5,"column_end":13,"is_primary":false,"text":[{"text":"use hag_core::core::AppBuilder;","highlight_start":5,"highlight_end":13}],"label":"one version of crate `kameo` used here, as a dependency of crate `hag_core`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs","byte_start":655,"byte_end":674,"line_start":26,"line_end":26,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"pub struct HassMgmt {","highlight_start":1,"highlight_end":20}],"label":"this type doesn't implement the required trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs","byte_start":334,"byte_end":378,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":false,"text":[{"text":"pub(crate) trait ActorState<A: Actor>: Sized {","highlight_start":1,"highlight_end":45}],"label":"this is the found trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"you can use `cargo tree` to explore your dependency tree","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `HassMgmt: Actor` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mhag-hvac/src/agents/hvac_agent.rs:370:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m370\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn continue_defrost(&mut self, _data: StateChangeData) -> WhateverResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `Actor` is not implemented for `HassMgmt`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: there are \u001b[0m\u001b[0m\u001b[1m\u001b[35mmultiple different versions\u001b[0m\u001b[0m of crate `\u001b[0m\u001b[0m\u001b[1m\u001b[35mkameo\u001b[0m\u001b[0m` in the dependency graph\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.14.0/src/actor.rs:110:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait Actor: Sized + Send + 'static {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mthis is the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/agents/mod.rs:4:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse kameo::actor::{pubsub::PubSub, ActorRef};\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a direct dependency of the current crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0mhag-hvac/src/main.rs:3:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse hag_core::core::AppBuilder;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mone version of crate `kameo` used here, as a dependency of crate `hag_core`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/dev/ha/hag/hag-core/src/hass/hass_mgmt.rs:26:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HassMgmt {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis type doesn't implement the required trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/kameo-0.16.0/src/actor/kind.rs:15:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub(crate) trait ActorState<A: Actor>: Sized {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is the found trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you can use `\u001b[0m\u001b[0m\u001b[1m\u001b[35mcargo tree\u001b[0m\u001b[0m` to explore your dependency tree\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 41 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 41 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0053, E0277, E0308, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0053, E0277, E0308, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0053`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0053`.\u001b[0m\n"}
