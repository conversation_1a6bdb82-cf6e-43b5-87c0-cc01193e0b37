{"rustc": 7501422222776872140, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9559541369283268958, "build_script_build", false, 5715963572879156567]], "local": [{"RerunIfChanged": {"output": "x86_64-unknown-linux-gnu/debug/build/libc-6468c03e23065cf0/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "RUST_LIBC_UNSTABLE_FREEBSD_VERSION", "val": null}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 0, "compile_kind": 0}