{"rustc": 7501422222776872140, "features": "[]", "declared_features": "[\"rustc-dep-of-std\", \"std\", \"wasm_js\"]", "target": 11669924403970522481, "profile": 14527943956391304460, "path": 16827875634720788215, "deps": [[9559541369283268958, "libc", false, 16410000204924258973], [10411997081178400487, "cfg_if", false, 12590011026901472265], [12546824318118147037, "build_script_build", false, 2964197464702439871]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/getrandom-73fff3729ef6cb4a/dep-lib-getrandom", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}