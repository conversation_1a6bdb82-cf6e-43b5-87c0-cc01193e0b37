{"rustc": 7501422222776872140, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 5638897767485582800, "path": 6686354174269659521, "deps": [[784494742817713399, "tower_service", false, 17869488951246623590], [828094305214142069, "http", false, 16333037177235917806], [4920660634395069245, "hyper_util", false, 15033321196632551318], [4958358643993419201, "rustls", false, 7593886401646986015], [9538054652646069845, "tokio", false, 1878114674913405815], [10629569228670356391, "futures_util", false, 17460236509704004415], [11957360342995674422, "hyper", false, 9758756471446036850], [12775413451309404286, "tokio_rustls", false, 6525106964295189504], [15594963368141592132, "webpki_roots", false, 3923385742532636025], [16009134724182327169, "pki_types", false, 10590706165122469042]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hyper-rustls-532b115b555fde4f/dep-lib-hyper_rustls", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}