{"rustc": 7501422222776872140, "features": "[\"alloc\", \"compiled_data\", \"std\"]", "declared_features": "[\"alloc\", \"compiled_data\", \"default\", \"std\"]", "target": 2602963282308965300, "profile": 5638897767485582800, "path": 9999604197349141153, "deps": [[5078124415930854154, "utf8_iter", false, 6354137002238265309], [6831611227313043439, "smallvec", false, 10363302276793725276], [15133333671624062849, "idna_adapter", false, 17909431802693120705]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/idna-6945192c856a759c/dep-lib-idna", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}