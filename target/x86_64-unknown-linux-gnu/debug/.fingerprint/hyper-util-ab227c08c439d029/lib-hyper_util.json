{"rustc": 7501422222776872140, "features": "[\"client\", \"client-legacy\", \"default\", \"http1\", \"tokio\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"client\", \"client-legacy\", \"default\", \"full\", \"http1\", \"http2\", \"server\", \"server-auto\", \"server-graceful\", \"service\", \"tokio\"]", "target": 11100538814903412163, "profile": 5638897767485582800, "path": 8873410972515250375, "deps": [[784494742817713399, "tower_service", false, 17869488951246623590], [828094305214142069, "http", false, 16333037177235917806], [1811549171721445101, "futures_channel", false, 5457328690396976161], [1906322745568073236, "pin_project_lite", false, 15490304558171860351], [3264248519883990135, "socket2", false, 6458634308615453185], [7489145127516980061, "bytes", false, 8891781225590645573], [8606274917505247608, "tracing", false, 7697175795440132406], [9538054652646069845, "tokio", false, 1878114674913405815], [10629569228670356391, "futures_util", false, 17460236509704004415], [11957360342995674422, "hyper", false, 9758756471446036850], [14084095096285906100, "http_body", false, 3264517790049908054]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hyper-util-ab227c08c439d029/dep-lib-hyper_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}