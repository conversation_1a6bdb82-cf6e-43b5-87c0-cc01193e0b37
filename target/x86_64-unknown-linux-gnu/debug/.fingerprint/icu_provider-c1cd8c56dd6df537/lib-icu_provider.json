{"rustc": 7501422222776872140, "features": "[\"macros\"]", "declared_features": "[\"bench\", \"datagen\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"experimental\", \"log_error_context\", \"logging\", \"macros\", \"serde\", \"std\", \"sync\"]", "target": *******************, "profile": 12197320342853292988, "path": 17836402260949316459, "deps": [[1218499983858120347, "icu_locid", false, 15947111935971695960], [*******************, "stable_deref_trait", false, 12049394007577355627], [5298260564258778412, "displaydoc", false, 16002632564527668653], [6573907571211658978, "zerofrom", false, 6284119821646000016], [8375043423015247637, "tinystr", false, 16686277106818743253], [11734826517668253792, "writeable", false, 13855740975110238387], [14072360194315679348, "zerovec", false, 3468964082961718517], [15837516966853249478, "icu_provider_macros", false, 17349388172813805745], [17505694197072478820, "yoke", false, 15454670870618906365]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/icu_provider-c1cd8c56dd6df537/dep-lib-icu_provider", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}