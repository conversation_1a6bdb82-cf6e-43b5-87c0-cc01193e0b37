{"rustc": 7501422222776872140, "features": "[\"alloc\", \"std\"]", "declared_features": "[\"alloc\", \"cfg-target-has-atomic\", \"default\", \"std\", \"unstable\"]", "target": 13518091470260541623, "profile": 2311051578282775386, "path": 3004378309798929865, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-task-b02ac6d64bfd5f13/dep-lib-futures_task", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}