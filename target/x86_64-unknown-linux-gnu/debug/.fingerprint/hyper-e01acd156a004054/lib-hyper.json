{"rustc": 7501422222776872140, "features": "[\"client\", \"default\", \"http1\"]", "declared_features": "[\"capi\", \"client\", \"default\", \"ffi\", \"full\", \"http1\", \"http2\", \"nightly\", \"server\", \"tracing\"]", "target": 9574292076208557625, "profile": 7662957420147757627, "path": 6552026783165165654, "deps": [[828094305214142069, "http", false, 16333037177235917806], [1569313478171189446, "want", false, 16935987482252733280], [1811549171721445101, "futures_channel", false, 5457328690396976161], [1906322745568073236, "pin_project_lite", false, 15490304558171860351], [6831611227313043439, "smallvec", false, 10363302276793725276], [7489145127516980061, "bytes", false, 8891781225590645573], [9272993696668697301, "itoa", false, 14710756667025354919], [9538054652646069845, "tokio", false, 1878114674913405815], [9632710759077199072, "httparse", false, 11625436544987768536], [10629569228670356391, "futures_util", false, 17460236509704004415], [14084095096285906100, "http_body", false, 3264517790049908054]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hyper-e01acd156a004054/dep-lib-hyper", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}