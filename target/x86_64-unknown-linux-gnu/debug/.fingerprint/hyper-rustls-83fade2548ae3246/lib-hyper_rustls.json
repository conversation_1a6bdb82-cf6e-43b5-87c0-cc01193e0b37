{"rustc": 7501422222776872140, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 12197320342853292988, "path": 6686354174269659521, "deps": [[784494742817713399, "tower_service", false, 8030510458800308454], [828094305214142069, "http", false, 3566128456673887320], [4920660634395069245, "hyper_util", false, 9028211139661815678], [4958358643993419201, "rustls", false, 3866447978844676929], [9538054652646069845, "tokio", false, 5241801424725673318], [10629569228670356391, "futures_util", false, 16545482580899732429], [11957360342995674422, "hyper", false, 2602327333493346112], [12775413451309404286, "tokio_rustls", false, 4466570803405992423], [15594963368141592132, "webpki_roots", false, 16547104312398465769], [16009134724182327169, "pki_types", false, 5429418792558785258]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/hyper-rustls-83fade2548ae3246/dep-lib-hyper_rustls", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/bin/mold", "-Zshare-generics=y"], "config": 15564086925336547597, "compile_kind": 13270707523875659407}